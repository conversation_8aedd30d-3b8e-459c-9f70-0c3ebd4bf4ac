<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新下载GLB模型测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .model-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            color: #333;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .model-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #1e3a8a;
        }
        
        .model-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .model-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 12px;
        }
        
        .model-size {
            background: #e0f2fe;
            color: #0277bd;
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .model-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-loaded {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .model-viewer {
            width: 100%;
            height: 200px;
            background: #f5f5f5;
            border-radius: 8px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .viewer-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #999;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .summary {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            color: #333;
            margin-top: 20px;
        }
        
        .summary h3 {
            margin-top: 0;
            color: #1e3a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 新下载的企业3D客服GLB模型测试</h1>
            <p>测试刚刚下载的6个企业客服形象GLB模型</p>
        </div>
        
        <div class="model-grid" id="model-grid">
            <!-- 模型卡片将通过JavaScript动态生成 -->
        </div>
        
        <div class="summary" id="summary">
            <h3>📊 测试总结</h3>
            <div id="summary-content">正在初始化测试...</div>
        </div>
    </div>

    <!-- Three.js 库 -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    
    <script>
        // 新下载的模型列表
        const NEW_MODELS = {
            'threejs_xbot_professional': {
                path: './models/threejs_xbot_professional.glb',
                name: 'Three.js专业Xbot',
                description: 'Three.js专业Xbot形象，适合企业应用，高质量全身模型',
                expectedSize: '2.79 MB'
            },
            'professional_uniform_avatar': {
                path: './models/professional_uniform_avatar.glb',
                name: '专业制服形象',
                description: '专业制服形象，适合正式客服场景，权威感强',
                expectedSize: '2.06 MB'
            },
            'readyplayerme_business_professional': {
                path: './models/readyplayerme_business_professional.glb',
                name: 'Ready Player Me商务专业',
                description: 'Ready Player Me商务专业形象，高质量逼真模型',
                expectedSize: '0.9 MB'
            },
            'enterprise_male_professional': {
                path: './models/enterprise_male_professional.glb',
                name: '企业男性专业形象',
                description: '专业男性商务形象，全身带动画，经典商务风格',
                expectedSize: '0.47 MB'
            },
            'business_professional_rigged': {
                path: './models/business_professional_rigged.glb',
                name: '商务专业绑定版',
                description: '商务专业人士，完整骨骼绑定，轻量级高性能',
                expectedSize: '0.05 MB'
            },
            'simple_business_avatar': {
                path: './models/simple_business_avatar.glb',
                name: '简洁商务形象',
                description: '简洁商务形象，轻量级全身模型，极小文件',
                expectedSize: '0.01 MB'
            }
        };
        
        class ModelTester {
            constructor() {
                this.loadedModels = 0;
                this.totalModels = Object.keys(NEW_MODELS).length;
                this.results = {};
                this.init();
            }
            
            init() {
                this.createModelCards();
                this.startTesting();
            }
            
            createModelCards() {
                const grid = document.getElementById('model-grid');
                
                Object.entries(NEW_MODELS).forEach(([id, model]) => {
                    const card = document.createElement('div');
                    card.className = 'model-card';
                    card.innerHTML = `
                        <div class="model-name">${model.name}</div>
                        <div class="model-description">${model.description}</div>
                        <div class="model-info">
                            <span class="model-size">${model.expectedSize}</span>
                            <span class="model-status status-loading" id="status-${id}">准备测试</span>
                        </div>
                        <div class="model-viewer" id="viewer-${id}">
                            <div class="viewer-placeholder">等待加载...</div>
                        </div>
                        <div>
                            <button class="btn btn-primary" onclick="tester.testModel('${id}')">测试加载</button>
                            <button class="btn btn-secondary" onclick="tester.showModelInfo('${id}')">详细信息</button>
                        </div>
                    `;
                    grid.appendChild(card);
                });
            }
            
            async startTesting() {
                console.log('🚀 开始测试新下载的GLB模型...');
                
                for (const [id, model] of Object.entries(NEW_MODELS)) {
                    await this.testModel(id);
                    await new Promise(resolve => setTimeout(resolve, 500)); // 间隔500ms
                }
                
                this.showSummary();
            }
            
            async testModel(modelId) {
                const model = NEW_MODELS[modelId];
                const statusElement = document.getElementById(`status-${modelId}`);
                const viewerElement = document.getElementById(`viewer-${modelId}`);
                
                console.log(`🔄 测试模型: ${model.name}`);
                
                // 更新状态为加载中
                statusElement.textContent = '加载中...';
                statusElement.className = 'model-status status-loading';
                viewerElement.innerHTML = '<div class="viewer-placeholder"><div class="loading-spinner"></div></div>';
                
                const startTime = performance.now();
                
                try {
                    // 创建Three.js场景
                    const scene = new THREE.Scene();
                    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
                    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                    
                    renderer.setSize(viewerElement.clientWidth, viewerElement.clientHeight);
                    renderer.setClearColor(0x000000, 0);
                    viewerElement.innerHTML = '';
                    viewerElement.appendChild(renderer.domElement);
                    
                    // 添加光照
                    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                    scene.add(ambientLight);
                    
                    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                    directionalLight.position.set(1, 1, 1);
                    scene.add(directionalLight);
                    
                    // 加载GLB模型
                    const loader = new THREE.GLTFLoader();
                    
                    const gltf = await new Promise((resolve, reject) => {
                        loader.load(
                            model.path,
                            resolve,
                            (progress) => {
                                const percent = Math.round((progress.loaded / progress.total) * 100);
                                statusElement.textContent = `加载中 ${percent}%`;
                            },
                            reject
                        );
                    });
                    
                    const loadTime = performance.now() - startTime;
                    
                    // 添加模型到场景
                    const modelObject = gltf.scene;
                    scene.add(modelObject);
                    
                    // 调整相机位置
                    const box = new THREE.Box3().setFromObject(modelObject);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());
                    
                    const maxDim = Math.max(size.x, size.y, size.z);
                    camera.position.set(center.x, center.y, center.z + maxDim * 2);
                    camera.lookAt(center);
                    
                    // 渲染场景
                    renderer.render(scene, camera);
                    
                    // 记录成功结果
                    this.results[modelId] = {
                        success: true,
                        loadTime: loadTime,
                        animations: gltf.animations ? gltf.animations.length : 0,
                        meshes: this.countMeshes(modelObject),
                        materials: this.countMaterials(modelObject)
                    };
                    
                    // 更新状态为成功
                    statusElement.textContent = `加载成功 (${Math.round(loadTime)}ms)`;
                    statusElement.className = 'model-status status-loaded';
                    
                    console.log(`✅ 模型加载成功: ${model.name}, 用时: ${Math.round(loadTime)}ms`);
                    
                } catch (error) {
                    console.error(`❌ 模型加载失败: ${model.name}`, error);
                    
                    // 记录失败结果
                    this.results[modelId] = {
                        success: false,
                        error: error.message,
                        loadTime: performance.now() - startTime
                    };
                    
                    // 更新状态为失败
                    statusElement.textContent = '加载失败';
                    statusElement.className = 'model-status status-error';
                    viewerElement.innerHTML = `<div class="viewer-placeholder">❌ 加载失败<br><small>${error.message}</small></div>`;
                }
                
                this.loadedModels++;
            }
            
            countMeshes(object) {
                let count = 0;
                object.traverse((child) => {
                    if (child.isMesh) count++;
                });
                return count;
            }
            
            countMaterials(object) {
                const materials = new Set();
                object.traverse((child) => {
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(mat => materials.add(mat));
                        } else {
                            materials.add(child.material);
                        }
                    }
                });
                return materials.size;
            }
            
            showSummary() {
                const successCount = Object.values(this.results).filter(r => r.success).length;
                const failCount = this.totalModels - successCount;
                const avgLoadTime = Object.values(this.results)
                    .filter(r => r.success)
                    .reduce((sum, r) => sum + r.loadTime, 0) / successCount;
                
                const summaryContent = document.getElementById('summary-content');
                summaryContent.innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <div>
                            <strong>总模型数:</strong> ${this.totalModels}
                        </div>
                        <div style="color: #155724;">
                            <strong>成功加载:</strong> ${successCount}
                        </div>
                        <div style="color: #721c24;">
                            <strong>加载失败:</strong> ${failCount}
                        </div>
                        <div>
                            <strong>平均加载时间:</strong> ${Math.round(avgLoadTime)}ms
                        </div>
                    </div>
                    
                    <h4>🎯 推荐使用的模型:</h4>
                    <ul>
                        ${Object.entries(this.results)
                            .filter(([id, result]) => result.success)
                            .sort((a, b) => a[1].loadTime - b[1].loadTime)
                            .slice(0, 3)
                            .map(([id, result]) => `
                                <li><strong>${NEW_MODELS[id].name}</strong> - 
                                    加载时间: ${Math.round(result.loadTime)}ms, 
                                    网格数: ${result.meshes}, 
                                    材质数: ${result.materials}
                                </li>
                            `).join('')}
                    </ul>
                    
                    <p><strong>建议:</strong> 优先使用加载时间短、网格数适中的模型以获得最佳性能。</p>
                `;
                
                console.log('📊 测试完成，结果:', this.results);
            }
            
            showModelInfo(modelId) {
                const model = NEW_MODELS[modelId];
                const result = this.results[modelId];
                
                let info = `模型: ${model.name}\n`;
                info += `路径: ${model.path}\n`;
                info += `描述: ${model.description}\n`;
                info += `预期大小: ${model.expectedSize}\n\n`;
                
                if (result) {
                    if (result.success) {
                        info += `✅ 加载成功\n`;
                        info += `加载时间: ${Math.round(result.loadTime)}ms\n`;
                        info += `动画数量: ${result.animations}\n`;
                        info += `网格数量: ${result.meshes}\n`;
                        info += `材质数量: ${result.materials}`;
                    } else {
                        info += `❌ 加载失败\n`;
                        info += `错误信息: ${result.error}`;
                    }
                } else {
                    info += `⏳ 尚未测试`;
                }
                
                alert(info);
            }
        }
        
        // 启动测试
        const tester = new ModelTester();
    </script>
</body>
</html>
