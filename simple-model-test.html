<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单GLB模型测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .model-selector {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .viewer-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #model-viewer {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        select, button {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            border: none;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .model-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .info-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
            font-size: 12px;
            text-transform: uppercase;
        }
        
        .info-value {
            font-size: 16px;
            color: #212529;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 企业GLB模型简单测试</h1>
            <p>测试新下载的企业客服3D模型加载情况</p>
        </div>
        
        <div class="model-selector">
            <h3>选择要测试的模型</h3>
            <div class="controls">
                <select id="model-select">
                    <option value="">请选择模型...</option>
                    <optgroup label="新下载的企业模型">
                        <option value="threejs_xbot_professional">Three.js专业Xbot (2.79MB)</option>
                        <option value="professional_uniform_avatar">专业制服形象 (2.06MB)</option>
                        <option value="readyplayerme_business_professional">Ready Player Me商务 (0.9MB)</option>
                        <option value="enterprise_male_professional">企业男性专业 (0.47MB)</option>
                        <option value="business_professional_rigged">商务专业绑定 (0.05MB)</option>
                        <option value="simple_business_avatar">简洁商务 (0.01MB)</option>
                    </optgroup>
                    <optgroup label="现有模型（测试对比）">
                        <option value="CesiumMan">CesiumMan</option>
                        <option value="RiggedFigure">RiggedFigure</option>
                        <option value="business_female">企业女客服</option>
                    </optgroup>
                </select>
                <button id="load-btn" disabled>加载模型</button>
                <button id="reset-btn">重置视图</button>
                <button id="test-all-btn">测试所有新模型</button>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="viewer-container">
            <h3>3D模型查看器</h3>
            <div id="model-viewer"></div>
            
            <div class="model-info" id="model-info" style="display: none;">
                <h4>模型信息</h4>
                <div class="info-grid" id="info-grid">
                    <!-- 动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 使用CDN的Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 模型配置
        const MODELS = {
            // 新下载的模型
            'threejs_xbot_professional': {
                path: './models/threejs_xbot_professional.glb',
                name: 'Three.js专业Xbot',
                size: '2.79MB'
            },
            'professional_uniform_avatar': {
                path: './models/professional_uniform_avatar.glb',
                name: '专业制服形象',
                size: '2.06MB'
            },
            'readyplayerme_business_professional': {
                path: './models/readyplayerme_business_professional.glb',
                name: 'Ready Player Me商务专业',
                size: '0.9MB'
            },
            'enterprise_male_professional': {
                path: './models/enterprise_male_professional.glb',
                name: '企业男性专业形象',
                size: '0.47MB'
            },
            'business_professional_rigged': {
                path: './models/business_professional_rigged.glb',
                name: '商务专业绑定版',
                size: '0.05MB'
            },
            'simple_business_avatar': {
                path: './models/simple_business_avatar.glb',
                name: '简洁商务形象',
                size: '0.01MB'
            },
            
            // 现有模型（对比测试）
            'CesiumMan': {
                path: './models/CesiumMan.glb',
                name: 'CesiumMan',
                size: '0.47MB'
            },
            'RiggedFigure': {
                path: './models/RiggedFigure.glb',
                name: 'RiggedFigure',
                size: '0.05MB'
            },
            'business_female': {
                path: './models/business_female.glb',
                name: '企业女客服',
                size: '0.01MB'
            }
        };
        
        class SimpleModelTester {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.currentModel = null;
                this.mixer = null;
                this.clock = new THREE.Clock();
                
                this.init();
                this.bindEvents();
            }
            
            init() {
                const container = document.getElementById('model-viewer');
                
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0xf0f0f0);
                
                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75, 
                    container.clientWidth / container.clientHeight, 
                    0.1, 
                    1000
                );
                this.camera.position.set(0, 1, 3);
                
                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(container.clientWidth, container.clientHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                container.appendChild(this.renderer.domElement);
                
                // 添加光照
                this.setupLighting();
                
                // 添加地面
                this.addGround();
                
                // 开始渲染循环
                this.animate();
                
                console.log('✅ 3D场景初始化完成');
            }
            
            setupLighting() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                this.scene.add(ambientLight);
                
                // 主光源
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(5, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(directionalLight);
                
                // 补光
                const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
                fillLight.position.set(-5, 5, -5);
                this.scene.add(fillLight);
            }
            
            addGround() {
                const geometry = new THREE.PlaneGeometry(10, 10);
                const material = new THREE.MeshLambertMaterial({ 
                    color: 0xffffff,
                    transparent: true,
                    opacity: 0.8
                });
                const ground = new THREE.Mesh(geometry, material);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }
            
            bindEvents() {
                // 模型选择
                document.getElementById('model-select').addEventListener('change', (e) => {
                    const loadBtn = document.getElementById('load-btn');
                    loadBtn.disabled = !e.target.value;
                });
                
                // 加载按钮
                document.getElementById('load-btn').addEventListener('click', () => {
                    const selectedModel = document.getElementById('model-select').value;
                    if (selectedModel) {
                        this.loadModel(selectedModel);
                    }
                });
                
                // 重置按钮
                document.getElementById('reset-btn').addEventListener('click', () => {
                    this.resetView();
                });
                
                // 测试所有模型
                document.getElementById('test-all-btn').addEventListener('click', () => {
                    this.testAllNewModels();
                });
                
                // 窗口大小调整
                window.addEventListener('resize', () => {
                    this.onWindowResize();
                });
            }
            
            showStatus(message, type = 'loading') {
                const statusEl = document.getElementById('status');
                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
                statusEl.style.display = 'block';
                console.log(`[${type.toUpperCase()}] ${message}`);
            }
            
            hideStatus() {
                document.getElementById('status').style.display = 'none';
            }
            
            async loadModel(modelId) {
                const modelConfig = MODELS[modelId];
                if (!modelConfig) {
                    this.showStatus('模型配置不存在', 'error');
                    return;
                }
                
                this.showStatus(`正在加载 ${modelConfig.name}...`, 'loading');
                const startTime = performance.now();
                
                try {
                    // 清除当前模型
                    this.clearCurrentModel();
                    
                    // 动态加载GLTFLoader
                    if (!window.GLTFLoader) {
                        await this.loadGLTFLoader();
                    }
                    
                    // 加载模型
                    const loader = new THREE.GLTFLoader();
                    
                    const gltf = await new Promise((resolve, reject) => {
                        loader.load(
                            modelConfig.path,
                            resolve,
                            (progress) => {
                                if (progress.total > 0) {
                                    const percent = Math.round((progress.loaded / progress.total) * 100);
                                    this.showStatus(`加载中 ${percent}% - ${modelConfig.name}`, 'loading');
                                }
                            },
                            reject
                        );
                    });
                    
                    const loadTime = performance.now() - startTime;
                    
                    // 添加模型到场景
                    this.currentModel = gltf.scene;
                    this.scene.add(this.currentModel);
                    
                    // 设置阴影
                    this.currentModel.traverse((child) => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                        }
                    });
                    
                    // 调整相机位置
                    this.fitCameraToModel(this.currentModel);
                    
                    // 设置动画
                    if (gltf.animations && gltf.animations.length > 0) {
                        this.mixer = new THREE.AnimationMixer(this.currentModel);
                        const action = this.mixer.clipAction(gltf.animations[0]);
                        action.play();
                    }
                    
                    // 显示成功信息
                    this.showStatus(`✅ 加载成功！用时 ${Math.round(loadTime)}ms`, 'success');
                    
                    // 显示模型信息
                    this.showModelInfo(modelConfig, gltf, loadTime);
                    
                    console.log(`✅ 模型加载成功: ${modelConfig.name}`);
                    
                } catch (error) {
                    console.error(`❌ 模型加载失败: ${modelConfig.name}`, error);
                    this.showStatus(`❌ 加载失败: ${error.message}`, 'error');
                }
            }
            
            async loadGLTFLoader() {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }
            
            clearCurrentModel() {
                if (this.currentModel) {
                    this.scene.remove(this.currentModel);
                    this.currentModel = null;
                }
                if (this.mixer) {
                    this.mixer = null;
                }
            }
            
            fitCameraToModel(model) {
                const box = new THREE.Box3().setFromObject(model);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                
                const maxDim = Math.max(size.x, size.y, size.z);
                const distance = maxDim * 2;
                
                this.camera.position.set(
                    center.x + distance * 0.5,
                    center.y + distance * 0.3,
                    center.z + distance
                );
                this.camera.lookAt(center);
            }
            
            showModelInfo(config, gltf, loadTime) {
                const infoEl = document.getElementById('model-info');
                const gridEl = document.getElementById('info-grid');
                
                // 统计信息
                let meshCount = 0;
                let materialCount = 0;
                const materials = new Set();
                
                gltf.scene.traverse((child) => {
                    if (child.isMesh) {
                        meshCount++;
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(mat => materials.add(mat));
                            } else {
                                materials.add(child.material);
                            }
                        }
                    }
                });
                materialCount = materials.size;
                
                gridEl.innerHTML = `
                    <div class="info-item">
                        <div class="info-label">模型名称</div>
                        <div class="info-value">${config.name}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">文件大小</div>
                        <div class="info-value">${config.size}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">加载时间</div>
                        <div class="info-value">${Math.round(loadTime)}ms</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">网格数量</div>
                        <div class="info-value">${meshCount}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">材质数量</div>
                        <div class="info-value">${materialCount}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">动画数量</div>
                        <div class="info-value">${gltf.animations ? gltf.animations.length : 0}</div>
                    </div>
                `;
                
                infoEl.style.display = 'block';
            }
            
            resetView() {
                this.camera.position.set(0, 1, 3);
                this.camera.lookAt(0, 0, 0);
                this.hideStatus();
                document.getElementById('model-info').style.display = 'none';
                document.getElementById('model-select').value = '';
                document.getElementById('load-btn').disabled = true;
            }
            
            async testAllNewModels() {
                const newModelIds = [
                    'simple_business_avatar',
                    'business_professional_rigged', 
                    'enterprise_male_professional',
                    'readyplayerme_business_professional',
                    'professional_uniform_avatar',
                    'threejs_xbot_professional'
                ];
                
                this.showStatus('开始批量测试新模型...', 'loading');
                
                const results = [];
                
                for (const modelId of newModelIds) {
                    try {
                        const startTime = performance.now();
                        await this.loadModel(modelId);
                        const loadTime = performance.now() - startTime;
                        
                        results.push({
                            id: modelId,
                            name: MODELS[modelId].name,
                            success: true,
                            loadTime: Math.round(loadTime)
                        });
                        
                        // 等待1秒再测试下一个
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                    } catch (error) {
                        results.push({
                            id: modelId,
                            name: MODELS[modelId].name,
                            success: false,
                            error: error.message
                        });
                    }
                }
                
                // 显示测试结果
                const successCount = results.filter(r => r.success).length;
                const summary = `测试完成！成功: ${successCount}/${results.length}`;
                this.showStatus(summary, successCount === results.length ? 'success' : 'error');
                
                console.log('📊 批量测试结果:', results);
            }
            
            animate() {
                requestAnimationFrame(() => this.animate());
                
                if (this.mixer) {
                    this.mixer.update(this.clock.getDelta());
                }
                
                this.renderer.render(this.scene, this.camera);
            }
            
            onWindowResize() {
                const container = document.getElementById('model-viewer');
                this.camera.aspect = container.clientWidth / container.clientHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(container.clientWidth, container.clientHeight);
            }
        }
        
        // 启动测试器
        window.addEventListener('load', () => {
            console.log('🚀 启动简单模型测试器...');
            window.modelTester = new SimpleModelTester();
        });
    </script>
</body>
</html>
