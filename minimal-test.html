<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小GLB测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .viewer {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            background: #f8f9fa;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 最小GLB加载测试</h1>
        <p>使用最简单的方法测试GLB模型加载</p>
        
        <div class="controls">
            <button onclick="testOnlineModel()">测试在线模型</button>
            <button onclick="testLocalModel('simple_business_avatar.glb')">测试本地最小模型</button>
            <button onclick="testLocalModel('CesiumMan.glb')">测试CesiumMan</button>
            <button onclick="testLocalModel('business_female.glb')">测试修复后的女性模型</button>
            <button onclick="clearTest()">清空</button>
        </div>
        
        <div id="viewer" class="viewer"></div>
        <div id="log" class="log"></div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        let scene, camera, renderer, currentModel;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function initScene() {
            const viewer = document.getElementById('viewer');
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf0f0f0);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, viewer.clientWidth / viewer.clientHeight, 0.1, 1000);
            camera.position.set(0, 1, 3);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(viewer.clientWidth, viewer.clientHeight);
            
            // 清空viewer并添加canvas
            viewer.innerHTML = '';
            viewer.appendChild(renderer.domElement);
            
            // 添加光照
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            scene.add(directionalLight);
            
            // 添加一个简单的立方体作为参考
            const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
            const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            const cube = new THREE.Mesh(geometry, material);
            cube.position.set(-2, 0, 0);
            scene.add(cube);
            
            // 开始渲染循环
            animate();
            
            log('✅ 3D场景初始化完成', 'success');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera);
        }
        
        function clearCurrentModel() {
            if (currentModel) {
                scene.remove(currentModel);
                currentModel = null;
            }
        }
        
        async function loadGLTFLoader() {
            if (window.GLTFLoader) return;
            
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js';
                script.onload = () => {
                    log('✅ GLTFLoader加载完成', 'success');
                    resolve();
                };
                script.onerror = () => {
                    log('❌ GLTFLoader加载失败', 'error');
                    reject(new Error('GLTFLoader加载失败'));
                };
                document.head.appendChild(script);
            });
        }
        
        async function testOnlineModel() {
            clearLog();
            log('🌐 测试在线GLB模型...', 'info');
            
            try {
                await loadGLTFLoader();
                clearCurrentModel();
                
                const loader = new THREE.GLTFLoader();
                const onlineUrl = 'https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Duck/glTF-Binary/Duck.glb';
                
                log(`加载在线模型: ${onlineUrl}`, 'info');
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load(
                        onlineUrl,
                        resolve,
                        (progress) => {
                            if (progress.total > 0) {
                                const percent = Math.round((progress.loaded / progress.total) * 100);
                                log(`加载进度: ${percent}%`, 'info');
                            }
                        },
                        (error) => {
                            log(`加载错误: ${error.message || error}`, 'error');
                            log(`错误类型: ${typeof error}`, 'error');
                            log(`错误详情: ${JSON.stringify(error, null, 2)}`, 'error');
                            reject(error);
                        }
                    );
                });
                
                currentModel = gltf.scene;
                scene.add(currentModel);
                
                // 调整相机位置
                const box = new THREE.Box3().setFromObject(currentModel);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                const maxDim = Math.max(size.x, size.y, size.z);
                
                camera.position.set(center.x, center.y, center.z + maxDim * 2);
                camera.lookAt(center);
                
                log('✅ 在线模型加载成功！', 'success');
                log(`模型信息: ${gltf.animations?.length || 0}个动画`, 'info');
                
            } catch (error) {
                log(`❌ 在线模型加载失败: ${error.message}`, 'error');
            }
        }
        
        async function testLocalModel(filename) {
            clearLog();
            log(`📁 测试本地模型: ${filename}`, 'info');
            
            try {
                await loadGLTFLoader();
                clearCurrentModel();
                
                // 首先检查文件是否存在
                log('检查文件是否存在...', 'info');
                const checkResponse = await fetch(`./models/${filename}`, { method: 'HEAD' });
                if (!checkResponse.ok) {
                    throw new Error(`文件不存在或无法访问: HTTP ${checkResponse.status}`);
                }
                
                const fileSize = checkResponse.headers.get('content-length');
                log(`✅ 文件存在，大小: ${fileSize ? (fileSize / 1024).toFixed(2) + 'KB' : '未知'}`, 'success');
                
                // 尝试读取文件内容
                log('读取文件内容...', 'info');
                const contentResponse = await fetch(`./models/${filename}`);
                if (!contentResponse.ok) {
                    throw new Error(`无法读取文件: HTTP ${contentResponse.status}`);
                }
                
                const arrayBuffer = await contentResponse.arrayBuffer();
                log(`✅ 文件读取成功，实际大小: ${(arrayBuffer.byteLength / 1024).toFixed(2)}KB`, 'success');
                
                // 检查文件头
                const view = new Uint8Array(arrayBuffer, 0, Math.min(20, arrayBuffer.byteLength));
                const hex = Array.from(view).map(b => b.toString(16).padStart(2, '0')).join(' ');
                log(`文件头: ${hex}`, 'info');
                
                const magic = new TextDecoder().decode(view.slice(0, 4));
                if (magic === 'glTF') {
                    log('✅ 有效的GLB文件头', 'success');
                } else {
                    log(`⚠️ 可疑的文件头: "${magic}"`, 'warning');
                }
                
                // 使用GLTFLoader加载
                log('使用GLTFLoader加载...', 'info');
                const loader = new THREE.GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load(
                        `./models/${filename}`,
                        (gltf) => {
                            log('✅ GLTFLoader成功回调', 'success');
                            resolve(gltf);
                        },
                        (progress) => {
                            if (progress.total > 0) {
                                const percent = Math.round((progress.loaded / progress.total) * 100);
                                log(`加载进度: ${percent}%`, 'info');
                            }
                        },
                        (error) => {
                            log(`❌ GLTFLoader错误回调`, 'error');
                            log(`错误消息: ${error.message || 'undefined'}`, 'error');
                            log(`错误名称: ${error.name || 'undefined'}`, 'error');
                            log(`错误类型: ${typeof error}`, 'error');
                            log(`错误构造函数: ${error.constructor?.name || 'undefined'}`, 'error');
                            log(`错误字符串: ${error.toString()}`, 'error');
                            
                            // 尝试获取更多错误信息
                            for (const key in error) {
                                if (error.hasOwnProperty(key)) {
                                    log(`错误属性 ${key}: ${error[key]}`, 'error');
                                }
                            }
                            
                            reject(error);
                        }
                    );
                });
                
                currentModel = gltf.scene;
                scene.add(currentModel);
                
                // 调整相机位置
                const box = new THREE.Box3().setFromObject(currentModel);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                const maxDim = Math.max(size.x, size.y, size.z);
                
                camera.position.set(center.x, center.y, center.z + maxDim * 2);
                camera.lookAt(center);
                
                // 统计模型信息
                let meshCount = 0;
                currentModel.traverse((child) => {
                    if (child.isMesh) meshCount++;
                });
                
                log(`✅ ${filename} 加载成功！`, 'success');
                log(`模型信息: ${meshCount}个网格, ${gltf.animations?.length || 0}个动画`, 'info');
                
            } catch (error) {
                log(`❌ ${filename} 加载失败: ${error.message}`, 'error');
                log(`完整错误: ${error.stack}`, 'error');
            }
        }
        
        function clearTest() {
            clearLog();
            clearCurrentModel();
            camera.position.set(0, 1, 3);
            camera.lookAt(0, 0, 0);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            if (typeof THREE === 'undefined') {
                log('❌ Three.js未加载', 'error');
                return;
            }
            
            log(`✅ Three.js已加载，版本: ${THREE.REVISION}`, 'success');
            initScene();
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const viewer = document.getElementById('viewer');
                camera.aspect = viewer.clientWidth / viewer.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
            }
        });
    </script>
</body>
</html>
