<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度调试GLB加载问题</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #000;
            padding: 20px;
            border: 2px solid #00ff00;
            border-radius: 10px;
        }
        
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #111;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .error { color: #ff4444; }
        .success { color: #44ff44; }
        .warning { color: #ffff44; }
        .info { color: #4444ff; }
        
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        
        button:hover {
            background: #00ff00;
            color: #000;
        }
        
        .viewer {
            width: 100%;
            height: 300px;
            border: 1px solid #333;
            background: #222;
            margin: 10px 0;
        }
        
        .file-info {
            background: #111;
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #00ff00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 GLB加载深度调试器</h1>
        <p>详细诊断GLB模型加载问题的根本原因</p>
        
        <div class="section">
            <h3>1. 环境检查</h3>
            <button onclick="checkEnvironment()">检查环境</button>
            <div id="env-log" class="log"></div>
        </div>
        
        <div class="section">
            <h3>2. 文件系统检查</h3>
            <button onclick="checkFileSystem()">检查文件</button>
            <div id="file-log" class="log"></div>
        </div>
        
        <div class="section">
            <h3>3. 网络请求测试</h3>
            <button onclick="testNetworkRequests()">测试网络</button>
            <div id="network-log" class="log"></div>
        </div>
        
        <div class="section">
            <h3>4. GLTFLoader详细测试</h3>
            <button onclick="testGLTFLoader()">测试加载器</button>
            <div id="loader-log" class="log"></div>
        </div>
        
        <div class="section">
            <h3>5. 逐步加载测试</h3>
            <button onclick="stepByStepTest()">逐步测试</button>
            <div id="step-log" class="log"></div>
            <div id="step-viewer" class="viewer"></div>
        </div>
        
        <div class="section">
            <h3>6. 错误捕获测试</h3>
            <button onclick="errorCaptureTest()">错误捕获</button>
            <div id="error-log" class="log"></div>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <script>
        // 日志函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            const logMessage = `[${timestamp}] ${message}\n`;
            
            element.innerHTML += `<span class="${className}">${logMessage}</span>`;
            element.scrollTop = element.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }
        
        // 1. 环境检查
        function checkEnvironment() {
            clearLog('env-log');
            log('env-log', '=== 开始环境检查 ===', 'info');
            
            // 检查Three.js
            if (typeof THREE !== 'undefined') {
                log('env-log', `✅ Three.js已加载 - 版本: ${THREE.REVISION}`, 'success');
                
                // 检查Three.js的具体功能
                try {
                    const scene = new THREE.Scene();
                    log('env-log', '✅ THREE.Scene 可用', 'success');
                    
                    const camera = new THREE.PerspectiveCamera();
                    log('env-log', '✅ THREE.PerspectiveCamera 可用', 'success');
                    
                    const renderer = new THREE.WebGLRenderer();
                    log('env-log', '✅ THREE.WebGLRenderer 可用', 'success');
                    
                } catch (error) {
                    log('env-log', `❌ Three.js基础功能错误: ${error.message}`, 'error');
                }
            } else {
                log('env-log', '❌ Three.js未加载', 'error');
                return;
            }
            
            // 检查WebGL
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                const version = gl.getParameter(gl.VERSION);
                const vendor = gl.getParameter(gl.VENDOR);
                const renderer = gl.getParameter(gl.RENDERER);
                log('env-log', `✅ WebGL支持: ${version}`, 'success');
                log('env-log', `   供应商: ${vendor}`, 'info');
                log('env-log', `   渲染器: ${renderer}`, 'info');
            } else {
                log('env-log', '❌ WebGL不支持', 'error');
            }
            
            // 检查浏览器信息
            log('env-log', `🌐 浏览器: ${navigator.userAgent}`, 'info');
            log('env-log', `📍 当前URL: ${window.location.href}`, 'info');
            log('env-log', `🔒 协议: ${window.location.protocol}`, 'info');
            
            // 检查CORS设置
            if (window.location.protocol === 'file:') {
                log('env-log', '⚠️ 使用file://协议，可能有CORS限制', 'warning');
            }
            
            log('env-log', '=== 环境检查完成 ===', 'info');
        }
        
        // 2. 文件系统检查
        async function checkFileSystem() {
            clearLog('file-log');
            log('file-log', '=== 开始文件系统检查 ===', 'info');
            
            const testFiles = [
                'business_female.glb',
                'simple_business_avatar.glb',
                'CesiumMan.glb',
                'RiggedFigure.glb'
            ];
            
            for (const filename of testFiles) {
                try {
                    log('file-log', `检查文件: ${filename}`, 'info');
                    
                    const response = await fetch(`./models/${filename}`, { 
                        method: 'HEAD',
                        cache: 'no-cache'
                    });
                    
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        const lastModified = response.headers.get('last-modified');
                        const contentType = response.headers.get('content-type');
                        
                        log('file-log', `✅ ${filename}`, 'success');
                        log('file-log', `   大小: ${size ? (size / 1024).toFixed(2) + 'KB' : '未知'}`, 'info');
                        log('file-log', `   修改时间: ${lastModified || '未知'}`, 'info');
                        log('file-log', `   内容类型: ${contentType || '未知'}`, 'info');
                        
                        // 检查文件内容的前几个字节
                        try {
                            const partialResponse = await fetch(`./models/${filename}`, {
                                headers: { 'Range': 'bytes=0-19' }
                            });
                            const buffer = await partialResponse.arrayBuffer();
                            const bytes = new Uint8Array(buffer);
                            const hex = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
                            log('file-log', `   文件头: ${hex}`, 'info');
                            
                            // 检查是否是有效的GLB文件
                            const magic = new TextDecoder().decode(bytes.slice(0, 4));
                            if (magic === 'glTF') {
                                log('file-log', `   ✅ 有效的GLB文件头`, 'success');
                            } else {
                                log('file-log', `   ❌ 无效的GLB文件头: ${magic}`, 'error');
                            }
                        } catch (rangeError) {
                            log('file-log', `   ⚠️ 无法读取文件头: ${rangeError.message}`, 'warning');
                        }
                        
                    } else {
                        log('file-log', `❌ ${filename} - HTTP ${response.status} ${response.statusText}`, 'error');
                    }
                } catch (error) {
                    log('file-log', `❌ ${filename} - ${error.message}`, 'error');
                }
            }
            
            log('file-log', '=== 文件系统检查完成 ===', 'info');
        }
        
        // 3. 网络请求测试
        async function testNetworkRequests() {
            clearLog('network-log');
            log('network-log', '=== 开始网络请求测试 ===', 'info');
            
            const testFile = 'simple_business_avatar.glb'; // 最小的文件
            
            try {
                log('network-log', `测试GET请求: ${testFile}`, 'info');
                
                const startTime = performance.now();
                const response = await fetch(`./models/${testFile}`);
                const endTime = performance.now();
                
                log('network-log', `请求时间: ${(endTime - startTime).toFixed(2)}ms`, 'info');
                log('network-log', `状态: ${response.status} ${response.statusText}`, 'info');
                log('network-log', `类型: ${response.type}`, 'info');
                log('network-log', `URL: ${response.url}`, 'info');
                
                if (response.ok) {
                    const blob = await response.blob();
                    log('network-log', `✅ 成功下载 ${blob.size} 字节`, 'success');
                    
                    // 尝试读取为ArrayBuffer
                    const arrayBuffer = await blob.arrayBuffer();
                    log('network-log', `✅ 成功转换为ArrayBuffer`, 'success');
                    
                    // 检查前几个字节
                    const view = new Uint8Array(arrayBuffer, 0, 20);
                    const hex = Array.from(view).map(b => b.toString(16).padStart(2, '0')).join(' ');
                    log('network-log', `文件内容: ${hex}`, 'info');
                    
                } else {
                    log('network-log', `❌ 请求失败: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log('network-log', `❌ 网络错误: ${error.message}`, 'error');
                log('network-log', `错误堆栈: ${error.stack}`, 'error');
            }
            
            log('network-log', '=== 网络请求测试完成 ===', 'info');
        }
        
        // 4. GLTFLoader详细测试
        async function testGLTFLoader() {
            clearLog('loader-log');
            log('loader-log', '=== 开始GLTFLoader测试 ===', 'info');
            
            try {
                // 动态加载GLTFLoader
                log('loader-log', '加载GLTFLoader...', 'info');
                
                if (!window.GLTFLoader) {
                    await new Promise((resolve, reject) => {
                        const script = document.createElement('script');
                        script.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js';
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                }
                
                log('loader-log', '✅ GLTFLoader脚本加载完成', 'success');
                
                // 检查GLTFLoader是否可用
                if (typeof THREE.GLTFLoader !== 'undefined') {
                    log('loader-log', '✅ THREE.GLTFLoader可用', 'success');
                    
                    // 创建加载器实例
                    const loader = new THREE.GLTFLoader();
                    log('loader-log', '✅ GLTFLoader实例创建成功', 'success');
                    
                    // 测试加载器的方法
                    if (typeof loader.load === 'function') {
                        log('loader-log', '✅ loader.load方法可用', 'success');
                    } else {
                        log('loader-log', '❌ loader.load方法不可用', 'error');
                    }
                    
                } else {
                    log('loader-log', '❌ THREE.GLTFLoader不可用', 'error');
                }
                
            } catch (error) {
                log('loader-log', `❌ GLTFLoader加载失败: ${error.message}`, 'error');
                log('loader-log', `错误堆栈: ${error.stack}`, 'error');
            }
            
            log('loader-log', '=== GLTFLoader测试完成 ===', 'info');
        }
        
        // 5. 逐步加载测试
        async function stepByStepTest() {
            clearLog('step-log');
            log('step-log', '=== 开始逐步加载测试 ===', 'info');
            
            const viewer = document.getElementById('step-viewer');
            viewer.innerHTML = '';
            
            try {
                // 步骤1: 创建场景
                log('step-log', '步骤1: 创建Three.js场景', 'info');
                const scene = new THREE.Scene();
                scene.background = new THREE.Color(0x222222);
                log('step-log', '✅ 场景创建成功', 'success');
                
                // 步骤2: 创建相机
                log('step-log', '步骤2: 创建相机', 'info');
                const camera = new THREE.PerspectiveCamera(75, viewer.clientWidth / viewer.clientHeight, 0.1, 1000);
                camera.position.set(0, 1, 3);
                log('step-log', '✅ 相机创建成功', 'success');
                
                // 步骤3: 创建渲染器
                log('step-log', '步骤3: 创建渲染器', 'info');
                const renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
                viewer.appendChild(renderer.domElement);
                log('step-log', '✅ 渲染器创建成功', 'success');
                
                // 步骤4: 添加光照
                log('step-log', '步骤4: 添加光照', 'info');
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(1, 1, 1);
                scene.add(directionalLight);
                log('step-log', '✅ 光照添加成功', 'success');
                
                // 步骤5: 加载GLTFLoader
                log('step-log', '步骤5: 准备GLTFLoader', 'info');
                if (!window.GLTFLoader) {
                    await new Promise((resolve, reject) => {
                        const script = document.createElement('script');
                        script.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js';
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                }
                const loader = new THREE.GLTFLoader();
                log('step-log', '✅ GLTFLoader准备完成', 'success');
                
                // 步骤6: 尝试加载最简单的模型
                log('step-log', '步骤6: 加载模型 (simple_business_avatar.glb)', 'info');
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load(
                        './models/simple_business_avatar.glb',
                        (gltf) => {
                            log('step-log', '✅ 模型加载成功回调触发', 'success');
                            resolve(gltf);
                        },
                        (progress) => {
                            if (progress.total > 0) {
                                const percent = Math.round((progress.loaded / progress.total) * 100);
                                log('step-log', `加载进度: ${percent}%`, 'info');
                            }
                        },
                        (error) => {
                            log('step-log', `❌ 模型加载失败: ${error.message || error}`, 'error');
                            log('step-log', `错误对象: ${JSON.stringify(error, null, 2)}`, 'error');
                            reject(error);
                        }
                    );
                });
                
                log('step-log', '✅ 模型加载完成', 'success');
                
                // 步骤7: 添加到场景
                log('step-log', '步骤7: 添加模型到场景', 'info');
                scene.add(gltf.scene);
                log('step-log', '✅ 模型添加到场景成功', 'success');
                
                // 步骤8: 渲染
                log('step-log', '步骤8: 渲染场景', 'info');
                renderer.render(scene, camera);
                log('step-log', '✅ 场景渲染成功', 'success');
                
                log('step-log', '🎉 逐步测试全部成功！', 'success');
                
            } catch (error) {
                log('step-log', `❌ 逐步测试失败: ${error.message}`, 'error');
                log('step-log', `错误类型: ${error.constructor.name}`, 'error');
                log('step-log', `错误堆栈: ${error.stack}`, 'error');
            }
            
            log('step-log', '=== 逐步加载测试完成 ===', 'info');
        }
        
        // 6. 错误捕获测试
        async function errorCaptureTest() {
            clearLog('error-log');
            log('error-log', '=== 开始错误捕获测试 ===', 'info');
            
            // 设置全局错误捕获
            const originalErrorHandler = window.onerror;
            const originalUnhandledRejection = window.onunhandledrejection;
            
            window.onerror = function(message, source, lineno, colno, error) {
                log('error-log', `🚨 全局错误: ${message}`, 'error');
                log('error-log', `   源文件: ${source}:${lineno}:${colno}`, 'error');
                if (error) {
                    log('error-log', `   错误对象: ${error.toString()}`, 'error');
                }
                return false;
            };
            
            window.onunhandledrejection = function(event) {
                log('error-log', `🚨 未处理的Promise拒绝: ${event.reason}`, 'error');
                event.preventDefault();
            };
            
            try {
                log('error-log', '测试故意触发的错误...', 'info');
                
                // 测试1: 加载不存在的文件
                log('error-log', '测试1: 加载不存在的文件', 'info');
                try {
                    const response = await fetch('./models/nonexistent.glb');
                    if (!response.ok) {
                        log('error-log', `✅ 正确捕获404错误: ${response.status}`, 'success');
                    }
                } catch (error) {
                    log('error-log', `✅ 正确捕获网络错误: ${error.message}`, 'success');
                }
                
                // 测试2: GLTFLoader错误处理
                log('error-log', '测试2: GLTFLoader错误处理', 'info');
                if (!window.GLTFLoader) {
                    await new Promise((resolve, reject) => {
                        const script = document.createElement('script');
                        script.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js';
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                }
                
                const loader = new THREE.GLTFLoader();
                try {
                    await new Promise((resolve, reject) => {
                        loader.load(
                            './models/nonexistent.glb',
                            resolve,
                            undefined,
                            reject
                        );
                    });
                } catch (error) {
                    log('error-log', `✅ 正确捕获GLTFLoader错误: ${error.message}`, 'success');
                }
                
                // 测试3: 加载损坏的文件
                log('error-log', '测试3: 加载business_female.glb (可能损坏)', 'info');
                try {
                    await new Promise((resolve, reject) => {
                        loader.load(
                            './models/business_female.glb',
                            resolve,
                            undefined,
                            (error) => {
                                log('error-log', `捕获到的错误信息:`, 'error');
                                log('error-log', `  message: ${error.message || 'undefined'}`, 'error');
                                log('error-log', `  name: ${error.name || 'undefined'}`, 'error');
                                log('error-log', `  type: ${typeof error}`, 'error');
                                log('error-log', `  constructor: ${error.constructor.name}`, 'error');
                                log('error-log', `  toString: ${error.toString()}`, 'error');
                                log('error-log', `  JSON: ${JSON.stringify(error, null, 2)}`, 'error');
                                reject(error);
                            }
                        );
                    });
                } catch (error) {
                    log('error-log', `✅ 成功捕获business_female.glb错误`, 'success');
                }
                
            } finally {
                // 恢复原始错误处理器
                window.onerror = originalErrorHandler;
                window.onunhandledrejection = originalUnhandledRejection;
            }
            
            log('error-log', '=== 错误捕获测试完成 ===', 'info');
        }
        
        // 页面加载完成后自动检查环境
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkEnvironment();
            }, 1000);
        });
    </script>
</body>
</html>
