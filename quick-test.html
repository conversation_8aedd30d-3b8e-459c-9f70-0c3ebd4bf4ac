<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速GLB测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .viewer {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            background: #f8f9fa;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .model-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速GLB模型测试</h1>
        <p>测试新下载的企业客服GLB模型是否能正常加载</p>
        
        <div class="controls">
            <button onclick="testModel('simple_business_avatar.glb')">测试最小模型 (0.01MB)</button>
            <button onclick="testModel('business_professional_rigged.glb')">测试轻量模型 (0.05MB)</button>
            <button onclick="testModel('enterprise_male_professional.glb')">测试中等模型 (0.47MB)</button>
            <button onclick="testModel('readyplayerme_business_professional.glb')">测试RPM模型 (0.9MB)</button>
            <button onclick="testModel('professional_uniform_avatar.glb')">测试制服模型 (2.06MB)</button>
            <button onclick="testModel('threejs_xbot_professional.glb')">测试Xbot模型 (2.79MB)</button>
        </div>
        
        <div class="controls">
            <button onclick="testExistingModel('CesiumMan.glb')" style="background: #6c757d;">对比: CesiumMan</button>
            <button onclick="testExistingModel('business_female.glb')" style="background: #6c757d;">对比: 企业女客服</button>
            <button onclick="clearViewer()">清空视图</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div id="viewer" class="viewer"></div>
        
        <div id="model-info" class="model-info" style="display: none;"></div>
    </div>

    <!-- 使用CDN的Three.js确保兼容性 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        let scene, camera, renderer, currentModel;
        
        // 初始化3D场景
        function initScene() {
            const viewer = document.getElementById('viewer');
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf0f0f0);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, viewer.clientWidth / viewer.clientHeight, 0.1, 1000);
            camera.position.set(0, 1, 3);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(viewer.clientWidth, viewer.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // 清空viewer并添加canvas
            viewer.innerHTML = '';
            viewer.appendChild(renderer.domElement);
            
            // 添加光照
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            // 添加地面
            const groundGeometry = new THREE.PlaneGeometry(10, 10);
            const groundMaterial = new THREE.MeshLambertMaterial({ 
                color: 0xffffff, 
                transparent: true, 
                opacity: 0.8 
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
            
            // 开始渲染循环
            animate();
            
            console.log('✅ 3D场景初始化完成');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera);
        }
        
        function showStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }
        
        function showModelInfo(info) {
            const infoEl = document.getElementById('model-info');
            infoEl.textContent = info;
            infoEl.style.display = 'block';
        }
        
        function hideModelInfo() {
            document.getElementById('model-info').style.display = 'none';
        }
        
        function clearCurrentModel() {
            if (currentModel) {
                scene.remove(currentModel);
                currentModel = null;
            }
        }
        
        function clearViewer() {
            clearCurrentModel();
            hideStatus();
            hideModelInfo();
            camera.position.set(0, 1, 3);
            camera.lookAt(0, 0, 0);
        }
        
        async function loadGLTFLoader() {
            if (window.GLTFLoader) {
                return; // 已经加载
            }
            
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js';
                script.onload = () => {
                    console.log('✅ GLTFLoader加载完成');
                    resolve();
                };
                script.onerror = () => {
                    console.error('❌ GLTFLoader加载失败');
                    reject(new Error('GLTFLoader加载失败'));
                };
                document.head.appendChild(script);
            });
        }
        
        async function testModel(filename) {
            showStatus(`正在加载 ${filename}...`, 'loading');
            hideModelInfo();
            
            const startTime = performance.now();
            
            try {
                // 确保GLTFLoader已加载
                await loadGLTFLoader();
                
                // 清除当前模型
                clearCurrentModel();
                
                // 创建加载器
                const loader = new THREE.GLTFLoader();
                
                // 加载模型
                const gltf = await new Promise((resolve, reject) => {
                    loader.load(
                        `./models/${filename}`,
                        resolve,
                        (progress) => {
                            if (progress.total > 0) {
                                const percent = Math.round((progress.loaded / progress.total) * 100);
                                showStatus(`加载中 ${percent}% - ${filename}`, 'loading');
                            }
                        },
                        reject
                    );
                });
                
                const loadTime = performance.now() - startTime;
                
                // 添加模型到场景
                currentModel = gltf.scene;
                scene.add(currentModel);
                
                // 设置阴影
                currentModel.traverse((child) => {
                    if (child.isMesh) {
                        child.castShadow = true;
                        child.receiveShadow = true;
                    }
                });
                
                // 调整相机位置
                const box = new THREE.Box3().setFromObject(currentModel);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                const maxDim = Math.max(size.x, size.y, size.z);
                
                camera.position.set(
                    center.x + maxDim * 0.5,
                    center.y + maxDim * 0.3,
                    center.z + maxDim * 1.5
                );
                camera.lookAt(center);
                
                // 统计模型信息
                let meshCount = 0;
                let materialCount = 0;
                const materials = new Set();
                
                currentModel.traverse((child) => {
                    if (child.isMesh) {
                        meshCount++;
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(mat => materials.add(mat));
                            } else {
                                materials.add(child.material);
                            }
                        }
                    }
                });
                materialCount = materials.size;
                
                // 显示成功信息
                showStatus(`✅ ${filename} 加载成功！用时: ${Math.round(loadTime)}ms`, 'success');
                
                // 显示模型详细信息
                const info = `模型文件: ${filename}
加载时间: ${Math.round(loadTime)}ms
网格数量: ${meshCount}
材质数量: ${materialCount}
动画数量: ${gltf.animations ? gltf.animations.length : 0}
场景对象: ${gltf.scene.children.length}
模型边界: ${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}`;
                
                showModelInfo(info);
                
                console.log(`✅ 模型加载成功: ${filename}`);
                console.log('模型详情:', {
                    filename,
                    loadTime: Math.round(loadTime),
                    meshCount,
                    materialCount,
                    animations: gltf.animations?.length || 0,
                    size: size
                });
                
            } catch (error) {
                console.error(`❌ 模型加载失败: ${filename}`, error);
                showStatus(`❌ ${filename} 加载失败: ${error.message}`, 'error');
                
                // 显示详细错误信息
                const errorInfo = `错误详情:
文件: ${filename}
错误类型: ${error.name || 'Unknown'}
错误信息: ${error.message}
堆栈: ${error.stack ? error.stack.substring(0, 200) + '...' : 'N/A'}`;
                
                showModelInfo(errorInfo);
            }
        }
        
        function testExistingModel(filename) {
            testModel(filename);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('🚀 页面加载完成，初始化3D场景...');
            
            // 检查Three.js是否加载
            if (typeof THREE === 'undefined') {
                showStatus('❌ Three.js未加载，请检查网络连接', 'error');
                return;
            }
            
            console.log(`✅ Three.js已加载，版本: ${THREE.REVISION}`);
            
            // 初始化场景
            try {
                initScene();
                showStatus('🎉 3D场景初始化完成，请选择模型进行测试', 'success');
                setTimeout(hideStatus, 3000);
            } catch (error) {
                console.error('❌ 场景初始化失败:', error);
                showStatus(`❌ 场景初始化失败: ${error.message}`, 'error');
            }
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const viewer = document.getElementById('viewer');
                camera.aspect = viewer.clientWidth / viewer.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
            }
        });
    </script>
</body>
</html>
