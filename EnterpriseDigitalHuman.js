/**
 * 企业级3D数字人核心类
 * 支持GLB/FBX模型加载、高级动画、表情控制、语音交互
 */
class EnterpriseDigitalHuman {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            width: options.width || 800,
            height: options.height || 600,
            modelPath: options.modelPath || './models/RiggedSimple.glb',
            enableAudio: options.enableAudio !== false,
            enableControls: options.enableControls !== false,
            enableShadows: false, // 禁用阴影以提升性能
            enablePostProcessing: false, // 禁用后处理以提升性能
            boundaryPadding: options.boundaryPadding || 50,
            targetFPS: 30, // 降低目标FPS
            pixelRatio: Math.min(window.devicePixelRatio, 1.5), // 限制像素比
            onProgress: options.onProgress || null,
            onLoaded: options.onLoaded || null,
            onError: options.onError || null,
            ...options
        };
        
        // Three.js核心对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.model = null;
        this.mixer = null;
        this.clock = new THREE.Clock();
        
        // 高级功能
        this.composer = null;
        this.modelLoader = null;
        this.fbxLoader = null;
        this.textureLoader = null;
        
        // 动画和控制
        this.animations = new Map();
        this.currentAnimation = null;
        this.isMoving = false;
        this.moveSpeed = 5;
        this.rotationSpeed = 0.1;
        this.walkSpeed = 3;
        this.isWalking = false;
        
        // 高级面部表情系统
        this.facialExpressionSystem = null;
        this.isSpeaking = false;
        
        // 语音适配器（适配公司语音系统）
        this.voiceAdapter = null;
        this.lipSyncData = null;
        
        // PBR增强系统引用
        this.pbrSystem = null;
        
        // 边界检测
        this.boundaries = {
            minX: -this.options.boundaryPadding,
            maxX: this.options.width - this.options.boundaryPadding,
            minZ: -this.options.boundaryPadding,
            maxZ: this.options.height - this.options.boundaryPadding
        };
        
        // 键盘状态和事件
        this.keys = {};
        this.eventListeners = {};
        
        // 性能监控
        this.stats = {
            fps: 0,
            frameCount: 0,
            lastTime: performance.now()
        };
        
        this.init();
    }
    
    /**
     * 初始化3D场景
     */
    init() {
        this.initScene();
        this.initCamera();
        this.initRenderer();
        this.initLights();
        this.initLoaders();
        this.initVoiceAdapter();
        this.initEventListeners();
        this.loadModel(this.options.modelPath);
        this.animate();
    }
    
    /**
     * 初始化场景
     */
    initScene() {
        this.scene = new THREE.Scene();
        
        // 创建渐变背景
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const context = canvas.getContext('2d');
        
        const gradient = context.createLinearGradient(0, 0, 0, 256);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(1, '#98FB98');
        
        context.fillStyle = gradient;
        context.fillRect(0, 0, 256, 256);
        
        const texture = new THREE.CanvasTexture(canvas);
        this.scene.background = texture;
        
        // 添加高质量地面
        this.createGround();
        
        // 添加环境效果
        this.createEnvironment();
    }
    
    /**
     * 创建地面
     */
    createGround() {
        const groundGeometry = new THREE.PlaneGeometry(2000, 2000, 100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xffffff,
            transparent: true,
            opacity: 0.8
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        ground.name = 'ground';
        
        // 添加网格效果
        const gridHelper = new THREE.GridHelper(2000, 50, 0x888888, 0xcccccc);
        gridHelper.material.transparent = true;
        gridHelper.material.opacity = 0.3;
        
        this.scene.add(ground);
        this.scene.add(gridHelper);
    }
    
    /**
     * 创建环境效果
     */
    createEnvironment() {
        // 添加粒子效果
        const particleCount = 100;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            positions[i * 3] = (Math.random() - 0.5) * 1000;
            positions[i * 3 + 1] = Math.random() * 500;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 1000;
        }
        
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 2,
            transparent: true,
            opacity: 0.6
        });
        
        const particleSystem = new THREE.Points(particles, particleMaterial);
        this.scene.add(particleSystem);
    }
    
    /**
     * 初始化相机
     */
    initCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            this.options.width / this.options.height,
            0.1,
            2000
        );
        this.camera.position.set(0, 160, 400);
        this.camera.lookAt(0, 100, 0);
    }
    
    /**
     * 初始化渲染器
     */
    initRenderer() {
        // 性能优化的渲染器配置
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: false, // 禁用抗锯齿以提升性能
            alpha: false, // 禁用透明度
            powerPreference: "default", // 使用默认功耗
            precision: "mediump" // 使用中等精度
        });
        
        this.renderer.setSize(this.options.width, this.options.height);
        this.renderer.setPixelRatio(this.options.pixelRatio || 1); // 限制像素比
        
        // 禁用阴影以提升性能
        this.renderer.shadowMap.enabled = false;
        
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
        
        this.container.appendChild(this.renderer.domElement);
    }
    
    /**
     * 初始化灯光
     */
    initLights() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // 主方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(100, 200, 100);
        directionalLight.castShadow = true;
        
        if (this.options.enableShadows) {
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 1000;
            directionalLight.shadow.camera.left = -200;
            directionalLight.shadow.camera.right = 200;
            directionalLight.shadow.camera.top = 200;
            directionalLight.shadow.camera.bottom = -200;
        }
        
        this.scene.add(directionalLight);
        
        // 补充光源
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-50, 100, -50);
        this.scene.add(fillLight);
        
        // 轮廓光
        const rimLight = new THREE.DirectionalLight(0x88ccff, 0.5);
        rimLight.position.set(0, 50, -200);
        this.scene.add(rimLight);
        
        // 动态点光源
        const pointLight = new THREE.PointLight(0xffffff, 0.8, 300);
        pointLight.position.set(0, 150, 0);
        this.scene.add(pointLight);
    }
    
    /**
     * 初始化模型加载器
     */
    initLoaders() {
        try {
            console.log('正在初始化加载器...');
            
            // 检查THREE.js是否已加载
            if (typeof THREE === 'undefined') {
                throw new Error('THREE.js 未加载');
            }
            
            console.log('THREE对象:', THREE);
            console.log('THREE.js 版本:', THREE.REVISION);
            
            // 详细检查GLTFLoader的可用性
            console.log('检查GLTFLoader可用性...');
            console.log('THREE.GLTFLoader:', typeof THREE.GLTFLoader);
            console.log('window.GLTFLoader:', typeof window.GLTFLoader);
            
            let LoaderClass = null;
            
            // 尝试多种方式获取GLTFLoader
            if (typeof THREE.GLTFLoader !== 'undefined') {
                LoaderClass = THREE.GLTFLoader;
                console.log('使用 THREE.GLTFLoader');
            } else if (typeof window.GLTFLoader !== 'undefined') {
                LoaderClass = window.GLTFLoader;
                console.log('使用 window.GLTFLoader');
                // 手动添加到THREE对象
                THREE.GLTFLoader = LoaderClass;
            } else {
                throw new Error('GLTFLoader 类不可用。请检查GLTFLoader.js是否正确加载');
            }
            
            // 尝试创建GLTFLoader实例
            try {
                this.modelLoader = new LoaderClass();
                console.log('GLTFLoader 初始化成功:', this.modelLoader);
                
                // 验证关键方法是否存在
                if (typeof this.modelLoader.load !== 'function') {
                    throw new Error('GLTFLoader.load 方法不存在');
                }
                
            } catch (loaderError) {
                throw new Error(`GLTFLoader 实例化失败: ${loaderError.message}`);
            }
            
            // FBX加载器（如果需要）
            if (window.THREE && THREE.FBXLoader) {
                this.fbxLoader = new THREE.FBXLoader();
                console.log('FBXLoader 初始化成功');
            } else {
                console.log('FBXLoader 不可用（可选）');
            }
            
            // 纹理加载器
            this.textureLoader = new THREE.TextureLoader();
            console.log('TextureLoader 初始化成功');
            
            console.log('所有加载器初始化完成');
            
        } catch (error) {
            console.error('加载器初始化失败:', error);
            console.error('错误堆栈:', error.stack);
            
            if (this.options.onError) {
                this.options.onError('加载器初始化失败: ' + error.message);
            }
        }
    }
    
    /**
     * 加载3D模型
     */
    loadModel(modelPath) {
        if (!modelPath) return;
        
        const fileExtension = modelPath.split('.').pop().toLowerCase();
        
        if (fileExtension === 'glb' || fileExtension === 'gltf') {
            this.loadGLTFModel(modelPath);
        } else if (fileExtension === 'fbx' && this.fbxLoader) {
            this.loadFBXModel(modelPath);
        } else {
            console.error('不支持的模型格式:', fileExtension);
            if (this.options.onError) {
                this.options.onError('不支持的模型格式: ' + fileExtension);
            }
        }
    }
    
    /**
     * 加载GLTF/GLB模型
     */
    loadGLTFModel(modelPath) {
        if (!this.modelLoader) {
            const errorMsg = 'GLTFLoader 未初始化';
            console.error(errorMsg);
            if (this.options.onError) {
                this.options.onError(errorMsg);
            }
            return;
        }
        
        console.log('开始加载模型:', modelPath);
        
        this.modelLoader.load(
            modelPath,
            (gltf) => {
                console.log('模型加载成功:', gltf);
                this.onModelLoaded(gltf.scene, gltf.animations);
            },
            (progress) => {
                if (progress.total > 0) {
                    const percentComplete = (progress.loaded / progress.total) * 100;
                    console.log('加载进度:', percentComplete + '%');
                    if (this.options.onProgress) {
                        this.options.onProgress(percentComplete);
                    }
                }
            },
            (error) => {
                console.error('GLTF模型加载失败:', error);
                console.error('模型路径:', modelPath);
                console.error('错误详情:', {
                    name: error?.name,
                    message: error?.message,
                    stack: error?.stack,
                    type: typeof error,
                    value: error
                });
                
                let errorMessage = '模型加载失败';
                if (error) {
                    if (typeof error === 'string') {
                        errorMessage += ': ' + error;
                    } else if (error.message) {
                        errorMessage += ': ' + error.message;
                    } else if (error.toString) {
                        errorMessage += ': ' + error.toString();
                    } else {
                        errorMessage += ': ' + JSON.stringify(error);
                    }
                } else {
                    errorMessage += ': 未知错误 (error对象为空)';
                }
                
                if (this.options.onError) {
                    this.options.onError(errorMessage);
                }
            }
        );
    }
    
    /**
     * 加载FBX模型
     */
    loadFBXModel(modelPath) {
        if (!this.fbxLoader) {
            console.error('FBX加载器未初始化');
            return;
        }
        
        this.fbxLoader.load(
            modelPath,
            (fbx) => {
                this.onModelLoaded(fbx, fbx.animations);
            },
            (progress) => {
                const percentComplete = (progress.loaded / progress.total) * 100;
                if (this.options.onProgress) {
                    this.options.onProgress(percentComplete);
                }
            },
            (error) => {
                console.error('FBX模型加载失败:', error);
                if (this.options.onError) {
                    this.options.onError('模型加载失败: ' + error.message);
                }
            }
        );
    }
    
    /**
     * 模型加载完成处理
     */
    onModelLoaded(model, animations) {
        // 清理旧模型
        if (this.model) {
            this.scene.remove(this.model);
        }
        
        this.model = model;
        
        // 设置阴影和材质
        this.model.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
                
                // 修复SkinnedMesh的skinning属性
                if (child.isSkinnedMesh && child.material) {
                    child.material.skinning = true;
                    console.log('✅ 修复SkinnedMesh skinning:', child.name);
                }
                
                // 优化材质
                if (child.material) {
                    child.material.needsUpdate = true;
                    
                    // 如果是数组材质，逐个处理
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => {
                            if (child.isSkinnedMesh) {
                                mat.skinning = true;
                            }
                            mat.needsUpdate = true;
                        });
                    }
                }
            }
        });
        
        // 添加到场景
        this.scene.add(this.model);
        
        // 计算模型边界并调整位置和相机
        this.adjustModelAndCamera();
        
        // 初始化动画
        this.initAnimations(animations);
        
        // 初始化高级面部表情系统
        this.initAdvancedFacialExpressions();
        
        // 自动播放待机动画
        this.playAnimation('idle', true);
        
        // 调用加载完成回调
        if (this.options.onLoaded) {
            this.options.onLoaded();
        }
        
        this.emit('modelLoaded', { model: this.model });
    }
    
    /**
     * 调整模型位置和相机设置
     */
    adjustModelAndCamera() {
        // 计算模型边界框
        const box = new THREE.Box3().setFromObject(this.model);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        
        console.log('模型边界框信息:');
        console.log('- 中心位置:', center);
        console.log('- 大小:', size);
        console.log('- 最小点:', box.min);
        console.log('- 最大点:', box.max);
        
        // 将模型居中到原点，但保持脚部在地面
        this.model.position.set(
            -center.x,  // X轴居中
            -box.min.y,  // Y轴：将模型底部放在地面(Y=0)
            -center.z   // Z轴居中
        );
        
        // 根据模型大小调整缩放
        const maxDimension = Math.max(size.x, size.y, size.z);
        let targetScale = 1;
        
        if (maxDimension < 50) {
            // 模型太小，放大
            targetScale = 100 / maxDimension;
            console.log(`模型太小 (${maxDimension.toFixed(2)})，放大至: ${targetScale.toFixed(2)}`);
        } else if (maxDimension > 200) {
            // 模型太大，缩小
            targetScale = 100 / maxDimension;
            console.log(`模型太大 (${maxDimension.toFixed(2)})，缩小至: ${targetScale.toFixed(2)}`);
        }
        
        this.model.scale.set(targetScale, targetScale, targetScale);
        
        // 重新计算调整后的边界框
        box.setFromObject(this.model);
        const newCenter = box.getCenter(new THREE.Vector3());
        const newSize = box.getSize(new THREE.Vector3());
        
        console.log('调整后的模型信息:');
        console.log('- 新中心位置:', newCenter);
        console.log('- 新大小:', newSize);
        console.log('- 缩放比例:', targetScale);
        
        // 调整相机位置以适应模型
        const distance = Math.max(newSize.x, newSize.z) * 2; // 距离是模型最大水平尺寸的2倍
        const height = newSize.y * 0.8; // 相机高度为模型高度的80%
        
        this.camera.position.set(
            0,
            height + 50,  // 稍微高于模型中心
            distance + 100  // 保持一定距离
        );
        
        // 让相机看向模型中心（地面以上一点）
        this.camera.lookAt(0, newSize.y * 0.4, 0);
        
        console.log('相机调整:');
        console.log('- 相机位置:', this.camera.position);
        console.log('- 看向位置: (0,', newSize.y * 0.4, ', 0)');
        
        // 更新相机投影矩阵
        this.camera.updateProjectionMatrix();
    }
    
    /**
     * 初始化动画
     */
    initAnimations(animations) {
        if (!animations || animations.length === 0) return;
        
        this.mixer = new THREE.AnimationMixer(this.model);
        this.animations.clear();
        
        animations.forEach((clip) => {
            const action = this.mixer.clipAction(clip);
            this.animations.set(clip.name, action);
        });
        
        // 创建默认动画映射
        this.createDefaultAnimations();
    }
    
    /**
     * 创建默认动画映射
     */
    createDefaultAnimations() {
        const animationMap = {
            'idle': ['Idle', 'idle', 'T-Pose', 'TPose'],
            'walk': ['Walk', 'walk', 'Walking'],
            'talk': ['Talk', 'talk', 'Speaking'],
            'wave': ['Wave', 'wave', 'Waving'],
            'greeting': ['Hello', 'greeting', 'Greet'],
            'thinking': ['Think', 'thinking', 'Pondering'],
            'presentation': ['Present', 'presentation', 'Explain'],
            'goodbye': ['Goodbye', 'bye', 'Farewell']
        };
        
        // 为找不到的动画创建默认动画
        Object.keys(animationMap).forEach(key => {
            if (!this.animations.has(key)) {
                const possibleNames = animationMap[key];
                for (const name of possibleNames) {
                    if (this.animations.has(name)) {
                        const action = this.animations.get(name);
                        this.animations.set(key, action);
                        break;
                    }
                }
            }
        });
    }
    
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        if (this.options.enableControls) {
            document.addEventListener('keydown', (e) => this.onKeyDown(e));
            document.addEventListener('keyup', (e) => this.onKeyUp(e));
            this.renderer.domElement.addEventListener('click', (e) => this.onMouseClick(e));
        }
        
        window.addEventListener('resize', () => this.onWindowResize());
    }
    
    /**
     * 播放动画
     */
    playAnimation(name, loop = false) {
        if (!this.mixer || !this.animations.has(name)) {
            console.warn('动画不存在:', name);
            return;
        }
        
        // 停止当前动画
        if (this.currentAnimation) {
            const currentAction = this.animations.get(this.currentAnimation);
            if (currentAction) {
                currentAction.fadeOut(0.5);
            }
        }
        
        // 播放新动画
        const action = this.animations.get(name);
        action.reset();
        action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce);
        action.fadeIn(0.5);
        action.play();
        
        this.currentAnimation = name;
        
        // 动画结束时的处理
        if (!loop) {
            action.clampWhenFinished = true;
            this.mixer.addEventListener('finished', (e) => {
                if (e.action === action) {
                    this.playAnimation('idle', true);
                }
            });
        }
    }
    
    /**
     * 停止动画
     */
    stopAnimation() {
        if (this.currentAnimation) {
            const action = this.animations.get(this.currentAnimation);
            if (action) {
                action.stop();
            }
            this.currentAnimation = null;
        }
    }
    
    /**
     * 设置表情（使用高级面部表情系统）
     */
    setEmotion(emotion, intensity = 0.8, options = {}) {
        if (this.facialExpressionSystem) {
            this.facialExpressionSystem.expressEmotion(emotion, intensity, options);
        } else {
            // 降级到基础表情系统
            this.setBasicEmotion(emotion);
        }
    }
    
    /**
     * 初始化高级面部表情系统
     */
    initAdvancedFacialExpressions() {
        if (typeof AdvancedFacialExpressionSystem !== 'undefined') {
            this.facialExpressionSystem = new AdvancedFacialExpressionSystem(this.model);
            
            // 通知面部表情系统模型已加载
            this.facialExpressionSystem.onModelLoaded(this.model);
            
            // 配置企业模式
            this.facialExpressionSystem.setConfig({
                businessMode: true,
                enableMicroExpressions: true,
                enableContextAwareness: true,
                enableCulturalAdaptation: true,
                responseLatency: 120
            });
            
            console.log('✅ 高级面部表情系统已初始化');
        } else {
            console.warn('⚠️ 高级面部表情系统未加载，使用基础表情功能');
        }
    }
    
    /**
     * 基础表情设置（降级功能）
     */
    setBasicEmotion(emotion) {
        if (!this.model) return;
        
        this.model.traverse((child) => {
            if (child.isMesh && child.morphTargetInfluences) {
                this.applyBasicEmotionMorph(child, emotion);
            }
        });
    }
    
    /**
     * 应用基础表情变形
     */
    applyBasicEmotionMorph(mesh, emotion) {
        const morphTargets = mesh.morphTargetDictionary;
        if (!morphTargets) return;
        
        // 重置所有表情
        if (mesh.morphTargetInfluences) {
            mesh.morphTargetInfluences.fill(0);
        }
        
        // 应用特定表情
        const emotionMorphs = {
            'happiness': ['smile', 'happy', 'joy'],
            'professional_smile': ['smile'],
            'surprised': ['surprise', 'shocked', 'amazed'],
            'thinking': ['concentrate', 'focus', 'ponder'],
            'neutral': []
        };
        
        const morphNames = emotionMorphs[emotion] || [];
        morphNames.forEach(morphName => {
            if (morphTargets[morphName] !== undefined) {
                mesh.morphTargetInfluences[morphTargets[morphName]] = 0.6; // 企业模式降低强度
            }
        });
    }
    
    /**
     * 初始化语音适配器
     */
    initVoiceAdapter() {
        try {
            if (typeof VoiceAdapter !== 'undefined') {
                this.voiceAdapter = new VoiceAdapter({
                    mode: 'company_integration',
                    fallbackToWebAPI: true,
                    debug: false
                });
                console.log('VoiceAdapter 初始化成功');
            } else {
                console.warn('VoiceAdapter 未加载，使用基础语音功能');
                this.voiceAdapter = this.createBasicVoiceAdapter();
            }
        } catch (error) {
            console.error('VoiceAdapter 初始化失败:', error);
            this.voiceAdapter = this.createBasicVoiceAdapter();
        }
    }
    
    /**
     * 创建基础语音适配器（降级方案）
     */
    createBasicVoiceAdapter() {
        return {
            async processVoice(text, options = {}) {
                try {
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(text);
                        utterance.lang = 'zh-CN';
                        utterance.rate = options.rate || 1;
                        utterance.pitch = options.pitch || 1;
                        
                        window.speechSynthesis.speak(utterance);
                        
                        return {
                            source: 'basic_web_api',
                            duration: text.length * 150, // 估算时长
                            playPromise: Promise.resolve()
                        };
                    } else {
                        console.warn('浏览器不支持语音合成');
                        return {
                            source: 'none',
                            duration: 0,
                            playPromise: Promise.resolve()
                        };
                    }
                } catch (error) {
                    console.error('基础语音播放失败:', error);
                    return {
                        source: 'error',
                        duration: 0,
                        playPromise: Promise.reject(error)
                    };
                }
            },
            
            getStatus() {
                return {
                    isInitialized: true,
                    mode: 'basic',
                    webAPIAvailable: 'speechSynthesis' in window
                };
            }
        };
    }
    
    /**
     * 语音播放（适配公司语音系统）
     */
    async speak(text, options = {}) {
        if (!this.options.enableAudio) return;
        
        this.isSpeaking = true;
        
        try {
            // 使用语音适配器处理语音
            const voiceResult = await this.voiceAdapter.processVoice(text, options);
            
            // 播放说话动画
            this.playAnimation('talk', true);
            
            // 使用高级面部表情系统分析文本并生成表情
            if (this.facialExpressionSystem) {
                await this.facialExpressionSystem.analyzeAndExpressFromText(text, {
                    isSpeaking: true,
                    speechDuration: voiceResult.duration,
                    context: options.context || {}
                });
            }
            
            this.emit('speakStart', { text, voiceResult });
            
            // 等待语音播放完成
            await voiceResult.playPromise;
            
        } catch (error) {
            console.error('语音播放错误:', error);
            this.emit('speakError', { error: error.message });
        } finally {
            this.isSpeaking = false;
            this.playAnimation('idle', true);
            
            // 重置到中性表情
            if (this.facialExpressionSystem) {
                this.facialExpressionSystem.resetToNeutral();
            }
            
            this.emit('speakEnd', { text });
        }
    }
    
    /**
     * 移动到指定位置
     */
    moveTo(x, z, duration = 1000) {
        if (!this.model) return;
        
        const clampedX = Math.max(this.boundaries.minX, Math.min(this.boundaries.maxX, x));
        const clampedZ = Math.max(this.boundaries.minZ, Math.min(this.boundaries.maxZ, z));
        
        const startPosition = this.model.position.clone();
        const targetPosition = new THREE.Vector3(clampedX, startPosition.y, clampedZ);
        
        const distance = startPosition.distanceTo(targetPosition);
        if (distance < 0.1) return;
        
        // 设置朝向
        this.model.lookAt(targetPosition.x, startPosition.y, targetPosition.z);
        
        // 播放行走动画
        this.playAnimation('walk', true);
        this.isMoving = true;
        
        // 平滑移动
        new TWEEN.Tween(startPosition)
            .to(targetPosition, duration)
            .easing(TWEEN.Easing.Quadratic.Out)
            .onUpdate(() => {
                this.model.position.copy(startPosition);
            })
            .onComplete(() => {
                this.isMoving = false;
                this.playAnimation('idle', true);
                this.emit('moveComplete', { position: this.getPosition() });
            })
            .start();
    }
    
    /**
     * 设置位置
     */
    setPosition(x, y, z) {
        if (!this.model) return;
        
        const position = new THREE.Vector3(x, y, z);
        if (this.checkBoundaries(position)) {
            this.model.position.copy(position);
        }
    }
    
    /**
     * 获取当前位置
     */
    getPosition() {
        if (!this.model) return { x: 0, y: 0, z: 0 };
        
        return {
            x: this.model.position.x,
            y: this.model.position.y,
            z: this.model.position.z
        };
    }
    
    /**
     * 边界检测
     */
    checkBoundaries(position) {
        return position.x >= this.boundaries.minX &&
               position.x <= this.boundaries.maxX &&
               position.z >= this.boundaries.minZ &&
               position.z <= this.boundaries.maxZ;
    }
    
    /**
     * 键盘事件处理
     */
    onKeyDown(event) {
        this.keys[event.key.toLowerCase()] = true;
        
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'w', 'a', 's', 'd'].includes(event.key.toLowerCase())) {
            event.preventDefault();
        }
    }
    
    onKeyUp(event) {
        this.keys[event.key.toLowerCase()] = false;
    }
    
    /**
     * 鼠标点击事件
     */
    onMouseClick(event) {
        if (!this.model) return;
        
        const mouse = new THREE.Vector2();
        const rect = this.renderer.domElement.getBoundingClientRect();
        
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);
        
        const groundObjects = this.scene.children.filter(obj => obj.name === 'ground');
        const intersects = raycaster.intersectObjects(groundObjects);
        
        if (intersects.length > 0) {
            const targetPosition = intersects[0].point;
            this.moveTo(targetPosition.x, targetPosition.z);
        }
    }
    
    /**
     * 窗口调整事件
     */
    onWindowResize() {
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
        
        // 通知PBR系统窗口大小变化
        if (this.pbrSystem && this.pbrSystem.onWindowResize) {
            this.pbrSystem.onWindowResize();
        }
        
        this.boundaries.maxX = width - this.options.boundaryPadding;
        this.boundaries.maxZ = height - this.options.boundaryPadding;
    }
    
    /**
     * 设置PBR增强系统
     */
    setPBRSystem(pbrSystem) {
        this.pbrSystem = pbrSystem;
        console.log('🌟 PBR系统已连接到数字人');
    }
    
    /**
     * 键盘移动处理
     */
    handleKeyboardMovement() {
        if (!this.model || this.isMoving) return;
        
        const moveVector = new THREE.Vector3();
        let shouldMove = false;
        
        if (this.keys['w'] || this.keys['arrowup']) {
            moveVector.z -= 1;
            shouldMove = true;
        }
        if (this.keys['s'] || this.keys['arrowdown']) {
            moveVector.z += 1;
            shouldMove = true;
        }
        if (this.keys['a'] || this.keys['arrowleft']) {
            moveVector.x -= 1;
            shouldMove = true;
        }
        if (this.keys['d'] || this.keys['arrowright']) {
            moveVector.x += 1;
            shouldMove = true;
        }
        
        if (shouldMove) {
            moveVector.normalize();
            const newPosition = this.model.position.clone().add(moveVector.multiplyScalar(this.moveSpeed));
            
            if (this.checkBoundaries(newPosition)) {
                this.model.position.copy(newPosition);
                
                if (moveVector.length() > 0) {
                    const targetRotation = Math.atan2(moveVector.x, moveVector.z);
                    this.model.rotation.y = targetRotation;
                }
                
                if (this.currentAnimation !== 'walk') {
                    this.playAnimation('walk', true);
                }
            }
        } else {
            if (this.currentAnimation !== 'idle') {
                this.playAnimation('idle', true);
            }
        }
    }
    
    /**
     * 获取FPS
     */
    getFPS() {
        return this.stats.fps;
    }
    
    /**
     * 更新性能统计
     */
    updateStats() {
        this.stats.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime > this.stats.lastTime + 1000) {
            this.stats.fps = Math.round((this.stats.frameCount * 1000) / (currentTime - this.stats.lastTime));
            this.stats.frameCount = 0;
            this.stats.lastTime = currentTime;
        }
    }
    
    /**
     * 主动画循环
     */
    animate() {
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        // 更新动画混合器
        if (this.mixer) {
            this.mixer.update(deltaTime);
        }
        
        // 更新Tween
        if (typeof TWEEN !== 'undefined') {
            TWEEN.update();
        }
        
        // 处理键盘移动
        if (this.options.enableControls) {
            this.handleKeyboardMovement();
        }
        
        // 更新性能统计
        this.updateStats();
        
        // 渲染场景（支持PBR增强渲染）
        if (this.pbrSystem && this.pbrSystem.render) {
            this.pbrSystem.render();
        } else {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    /**
     * 事件发射器
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }
    
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => callback(data));
        }
    }
    
    /**
     * 重新加载模型（用于模型切换）
     */
    reloadModel(newModelPath) {
        console.log('🔄 重新加载模型:', newModelPath);
        
        // 清理当前模型
        if (this.model) {
            this.scene.remove(this.model);
            this.model = null;
        }
        
        // 清理动画
        if (this.mixer) {
            try {
                this.mixer.stopAllAction();
            } catch (error) {
                console.warn('清理动画失败:', error);
            }
            this.mixer = null;
        }
        
        // 清理动画数据
        this.animations.clear();
        this.currentAnimation = null;
        
        // 加载新模型
        this.loadModel(newModelPath);
    }
    
    /**
     * 销毁实例
     */
    destroy() {
        // 停止动画
        if (this.mixer) {
            try {
                this.mixer.stopAllAction();
            } catch (error) {
                console.warn('销毁动画失败:', error);
            }
        }
        
        // 停止语音
        if (this.isSpeaking) {
            speechSynthesis.cancel();
        }
        
        // 移除事件监听器
        document.removeEventListener('keydown', this.onKeyDown);
        document.removeEventListener('keyup', this.onKeyUp);
        window.removeEventListener('resize', this.onWindowResize);
        
        // 清理场景
        if (this.scene) {
            this.scene.clear();
        }
        
        // 移除渲染器
        if (this.renderer && this.container) {
            this.container.removeChild(this.renderer.domElement);
            this.renderer.dispose();
        }
    }
    
    /**
     * 播放手势动画
     */
    playHandGesture(gestureName) {
        if (!this.model) {
            console.warn('模型未加载，无法播放手势');
            return;
        }
        
        console.log('播放手势:', gestureName);
        
        // 手势动画映射
        const gestureAnimations = {
            'wave_right': 'greeting',
            'wave_left': 'greeting',
            'point_right': 'presentation',
            'point_left': 'presentation',
            'thumbs_up': 'happiness',
            'ok_sign': 'happiness',
            'clap': 'celebration',
            'shake_hands': 'greeting',
            'hold_object': 'presentation'
        };
        
        const animationName = gestureAnimations[gestureName] || 'idle';
        this.playAnimation(animationName);
        
        // 手势动画持续2秒后返回待机状态
        setTimeout(() => {
            this.playAnimation('idle');
        }, 2000);
    }
    
    /**
     * 向前行走
     */
    walkForward() {
        if (!this.model) {
            console.warn('模型未加载，无法行走');
            return;
        }
        
        this.isWalking = true;
        
        // 向前移动
        const moveDistance = this.walkSpeed;
        const currentPosition = this.model.position;
        
        // 根据当前朝向移动
        const direction = new THREE.Vector3(0, 0, -1);
        direction.applyQuaternion(this.model.quaternion);
        
        const targetPosition = currentPosition.clone().add(direction.multiplyScalar(moveDistance));
        
        // 使用Tween进行平滑移动
        if (window.TWEEN) {
            new TWEEN.Tween(currentPosition)
                .to(targetPosition, 1000)
                .easing(TWEEN.Easing.Quadratic.Out)
                .onComplete(() => {
                    this.isWalking = false;
                })
                .start();
        } else {
            // 直接设置位置
            this.model.position.copy(targetPosition);
            this.isWalking = false;
        }
        
        console.log('向前行走');
    }
    
    /**
     * 向后行走
     */
    walkBackward() {
        if (!this.model) {
            console.warn('模型未加载，无法行走');
            return;
        }
        
        this.isWalking = true;
        
        // 向后移动
        const moveDistance = this.walkSpeed;
        const currentPosition = this.model.position;
        
        // 根据当前朝向反方向移动
        const direction = new THREE.Vector3(0, 0, 1);
        direction.applyQuaternion(this.model.quaternion);
        
        const targetPosition = currentPosition.clone().add(direction.multiplyScalar(moveDistance));
        
        // 使用Tween进行平滑移动
        if (window.TWEEN) {
            new TWEEN.Tween(currentPosition)
                .to(targetPosition, 1000)
                .easing(TWEEN.Easing.Quadratic.Out)
                .onComplete(() => {
                    this.isWalking = false;
                })
                .start();
        } else {
            // 直接设置位置
            this.model.position.copy(targetPosition);
            this.isWalking = false;
        }
        
        console.log('向后行走');
    }
    
    /**
     * 向左转
     */
    turnLeft() {
        if (!this.model) {
            console.warn('模型未加载，无法转向');
            return;
        }
        
        const currentRotation = this.model.rotation;
        const targetRotation = currentRotation.y + Math.PI / 4; // 45度
        
        // 使用Tween进行平滑旋转
        if (window.TWEEN) {
            new TWEEN.Tween({ rotation: currentRotation.y })
                .to({ rotation: targetRotation }, 500)
                .easing(TWEEN.Easing.Quadratic.Out)
                .onUpdate((obj) => {
                    this.model.rotation.y = obj.rotation;
                })
                .start();
        } else {
            // 直接设置旋转
            this.model.rotation.y = targetRotation;
        }
        
        console.log('向左转');
    }
    
    /**
     * 向右转
     */
    turnRight() {
        if (!this.model) {
            console.warn('模型未加载，无法转向');
            return;
        }
        
        const currentRotation = this.model.rotation;
        const targetRotation = currentRotation.y - Math.PI / 4; // -45度
        
        // 使用Tween进行平滑旋转
        if (window.TWEEN) {
            new TWEEN.Tween({ rotation: currentRotation.y })
                .to({ rotation: targetRotation }, 500)
                .easing(TWEEN.Easing.Quadratic.Out)
                .onUpdate((obj) => {
                    this.model.rotation.y = obj.rotation;
                })
                .start();
        } else {
            // 直接设置旋转
            this.model.rotation.y = targetRotation;
        }
        
        console.log('向右转');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnterpriseDigitalHuman;
} else {
    window.EnterpriseDigitalHuman = EnterpriseDigitalHuman;
}