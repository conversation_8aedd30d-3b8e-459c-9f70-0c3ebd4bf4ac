<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GLB模型调试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        #viewer {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 GLB模型加载调试</h1>
        
        <div class="test-section">
            <h3>1. 环境检查</h3>
            <button onclick="checkEnvironment()">检查环境</button>
            <div id="env-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 文件存在性检查</h3>
            <button onclick="checkFiles()">检查模型文件</button>
            <div id="file-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Three.js加载测试</h3>
            <button onclick="testThreeJS()">测试Three.js</button>
            <div id="threejs-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 简单模型加载测试</h3>
            <button onclick="testSimpleModel()">测试最小模型</button>
            <div id="simple-result" class="test-result"></div>
            <div id="viewer"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 新下载模型测试</h3>
            <button onclick="testNewModels()">测试新模型</button>
            <div id="new-result" class="test-result"></div>
        </div>
    </div>

    <!-- 本地Three.js库 -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    
    <script>
        // 新下载的模型列表
        const NEW_MODELS = [
            'threejs_xbot_professional.glb',
            'professional_uniform_avatar.glb', 
            'readyplayerme_business_professional.glb',
            'enterprise_male_professional.glb',
            'business_professional_rigged.glb',
            'simple_business_avatar.glb'
        ];
        
        // 现有模型（用于对比）
        const EXISTING_MODELS = [
            'CesiumMan.glb',
            'RiggedFigure.glb',
            'business_female.glb'
        ];
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            element.textContent += logMessage;
            element.className = `test-result ${type}`;
            
            console.log(message);
        }
        
        function clearLog(elementId) {
            document.getElementById(elementId).textContent = '';
        }
        
        function checkEnvironment() {
            clearLog('env-result');
            
            log('env-result', '开始环境检查...', 'info');
            
            // 检查Three.js
            if (typeof THREE !== 'undefined') {
                log('env-result', `✅ Three.js已加载 - 版本: ${THREE.REVISION}`, 'success');
            } else {
                log('env-result', '❌ Three.js未加载', 'error');
                return;
            }
            
            // 检查GLTFLoader
            if (typeof THREE.GLTFLoader !== 'undefined') {
                log('env-result', '✅ GLTFLoader已加载', 'success');
            } else {
                log('env-result', '❌ GLTFLoader未加载', 'error');
                return;
            }
            
            // 检查WebGL支持
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                log('env-result', '✅ WebGL支持正常', 'success');
            } else {
                log('env-result', '❌ WebGL不支持', 'error');
            }
            
            // 检查当前路径
            log('env-result', `📍 当前页面路径: ${window.location.href}`, 'info');
            
            log('env-result', '环境检查完成！', 'success');
        }
        
        async function checkFiles() {
            clearLog('file-result');
            log('file-result', '开始检查文件存在性...', 'info');
            
            const allModels = [...NEW_MODELS, ...EXISTING_MODELS];
            
            for (const model of allModels) {
                try {
                    const response = await fetch(`./models/${model}`, { method: 'HEAD' });
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        const sizeText = size ? ` (${(size / 1024 / 1024).toFixed(2)}MB)` : '';
                        log('file-result', `✅ ${model}${sizeText}`, 'success');
                    } else {
                        log('file-result', `❌ ${model} - HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    log('file-result', `❌ ${model} - ${error.message}`, 'error');
                }
            }
            
            log('file-result', '文件检查完成！', 'info');
        }
        
        function testThreeJS() {
            clearLog('threejs-result');
            log('threejs-result', '开始Three.js基础测试...', 'info');
            
            try {
                // 创建基础场景
                const scene = new THREE.Scene();
                log('threejs-result', '✅ Scene创建成功', 'success');
                
                const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
                log('threejs-result', '✅ Camera创建成功', 'success');
                
                const renderer = new THREE.WebGLRenderer();
                log('threejs-result', '✅ Renderer创建成功', 'success');
                
                // 创建简单几何体
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                log('threejs-result', '✅ 基础几何体创建成功', 'success');
                
                // 创建GLTFLoader
                const loader = new THREE.GLTFLoader();
                log('threejs-result', '✅ GLTFLoader实例化成功', 'success');
                
                log('threejs-result', 'Three.js基础测试通过！', 'success');
                
            } catch (error) {
                log('threejs-result', `❌ Three.js测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testSimpleModel() {
            clearLog('simple-result');
            log('simple-result', '开始测试最简单的模型...', 'info');
            
            const viewer = document.getElementById('viewer');
            viewer.innerHTML = '';
            
            try {
                // 创建场景
                const scene = new THREE.Scene();
                scene.background = new THREE.Color(0xf0f0f0);
                
                const camera = new THREE.PerspectiveCamera(75, viewer.clientWidth / viewer.clientHeight, 0.1, 1000);
                camera.position.z = 3;
                
                const renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
                viewer.appendChild(renderer.domElement);
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(1, 1, 1);
                scene.add(directionalLight);
                
                log('simple-result', '✅ 3D场景设置完成', 'success');
                
                // 测试最小的模型
                const testModel = 'simple_business_avatar.glb'; // 0.01MB
                log('simple-result', `开始加载: ${testModel}`, 'info');
                
                const loader = new THREE.GLTFLoader();
                const startTime = performance.now();
                
                loader.load(
                    `./models/${testModel}`,
                    (gltf) => {
                        const loadTime = performance.now() - startTime;
                        log('simple-result', `✅ 模型加载成功！用时: ${Math.round(loadTime)}ms`, 'success');
                        
                        const model = gltf.scene;
                        scene.add(model);
                        
                        // 调整相机位置
                        const box = new THREE.Box3().setFromObject(model);
                        const center = box.getCenter(new THREE.Vector3());
                        const size = box.getSize(new THREE.Vector3());
                        const maxDim = Math.max(size.x, size.y, size.z);
                        camera.position.set(center.x, center.y, center.z + maxDim * 2);
                        camera.lookAt(center);
                        
                        // 渲染
                        renderer.render(scene, camera);
                        
                        log('simple-result', '✅ 模型渲染成功！', 'success');
                        
                        // 统计信息
                        let meshCount = 0;
                        model.traverse((child) => {
                            if (child.isMesh) meshCount++;
                        });
                        
                        log('simple-result', `📊 模型信息: ${meshCount}个网格, ${gltf.animations.length}个动画`, 'info');
                    },
                    (progress) => {
                        if (progress.total > 0) {
                            const percent = Math.round((progress.loaded / progress.total) * 100);
                            log('simple-result', `加载进度: ${percent}%`, 'info');
                        }
                    },
                    (error) => {
                        log('simple-result', `❌ 模型加载失败: ${error.message}`, 'error');
                        console.error('详细错误:', error);
                    }
                );
                
            } catch (error) {
                log('simple-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testNewModels() {
            clearLog('new-result');
            log('new-result', '开始测试所有新下载的模型...', 'info');
            
            const loader = new THREE.GLTFLoader();
            const results = [];
            
            for (const model of NEW_MODELS) {
                try {
                    log('new-result', `测试: ${model}`, 'info');
                    const startTime = performance.now();
                    
                    await new Promise((resolve, reject) => {
                        loader.load(
                            `./models/${model}`,
                            (gltf) => {
                                const loadTime = performance.now() - startTime;
                                log('new-result', `✅ ${model} - ${Math.round(loadTime)}ms`, 'success');
                                results.push({ model, success: true, loadTime });
                                resolve();
                            },
                            undefined,
                            (error) => {
                                log('new-result', `❌ ${model} - ${error.message}`, 'error');
                                results.push({ model, success: false, error: error.message });
                                reject(error);
                            }
                        );
                    });
                    
                } catch (error) {
                    // 错误已在上面记录
                }
                
                // 短暂延迟避免过快请求
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            // 总结
            const successCount = results.filter(r => r.success).length;
            log('new-result', `\n📊 测试完成: ${successCount}/${NEW_MODELS.length} 成功`, 
                successCount === NEW_MODELS.length ? 'success' : 'error');
            
            if (successCount > 0) {
                const avgTime = results.filter(r => r.success)
                    .reduce((sum, r) => sum + r.loadTime, 0) / successCount;
                log('new-result', `⏱️ 平均加载时间: ${Math.round(avgTime)}ms`, 'info');
            }
        }
        
        // 页面加载完成后自动检查环境
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 500);
        });
    </script>
</body>
</html>
