<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证 - 数字人模型加载</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; margin: 10px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; background: #007bff; color: white; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .info { padding: 10px; margin: 10px 0; background: #e9ecef; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数字人系统修复验证</h1>
        
        <div class="info">
            <h3>修复内容:</h3>
            <ul>
                <li>✅ 修复 AnimationMixer.stopAllActions() → stopAllAction()</li>
                <li>✅ 修复 SkinnedMesh 材质 skinning 属性</li>
                <li>✅ 添加模型重载功能 reloadModel()</li>
                <li>✅ 完善错误处理和日志记录</li>
            </ul>
        </div>

        <div class="test-section" id="test-gltf-loader">
            <h3>🔍 1. GLTFLoader 可用性检测</h3>
            <div class="log" id="gltf-log"></div>
            <button class="btn" onclick="testGLTFLoader()">测试 GLTFLoader</button>
        </div>

        <div class="test-section" id="test-three-js">
            <h3>🔍 2. Three.js 核心功能检测</h3>
            <div class="log" id="threejs-log"></div>
            <button class="btn" onclick="testThreeJS()">测试 Three.js</button>
        </div>

        <div class="test-section" id="test-model-paths">
            <h3>🔍 3. 模型文件路径检测</h3>
            <div class="log" id="models-log"></div>
            <button class="btn" onclick="testModelPaths()">检测模型文件</button>
        </div>

        <div class="test-section" id="test-digital-human">
            <h3>🔍 4. 数字人系统初始化测试</h3>
            <div class="log" id="human-log"></div>
            <div id="test-viewport" style="width: 400px; height: 300px; border: 1px solid #ccc; margin: 10px 0;"></div>
            <button class="btn" onclick="testDigitalHuman()">测试数字人初始化</button>
        </div>

        <div class="test-section">
            <h3>📋 完整测试报告</h3>
            <div class="log" id="report-log"></div>
            <button class="btn" onclick="generateReport()">生成报告</button>
        </div>
    </div>

    <!-- 加载必要的库 -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    <script src="./EnterpriseDigitalHuman.js"></script>

    <script>
        // 完整模型路径配置
        const MODEL_PATHS = {
            // 企业级客服模型
            'business_female': './models/business_female.glb',
            'business_male': './models/business_male.glb',
            'enterprise_female_rigged': './models/enterprise_female_rigged.glb',
            'enterprise_male_simple': './models/enterprise_male_simple.glb',
            
            // 专业形象模型
            'professional_avatar': './models/professional_avatar.glb',
            'realistic_human_backup': './models/realistic_human_backup.glb',
            'cesium_man_professional': './models/cesium_man_professional.glb',
            'CesiumMan': './models/CesiumMan.glb',
            
            // 演示和特效模型
            'expression_demo': './models/expression_demo.glb',
            'fox_expression_demo': './models/fox_expression_demo.glb',
            'morph_animation_demo': './models/morph_animation_demo.glb',
            
            // 基础和测试模型
            'RiggedSimple': './models/RiggedSimple.glb',
            'RiggedSimple_updated': './models/RiggedSimple_updated.glb',
            'RiggedFigure': './models/RiggedFigure.glb',
            
            // 备用和特殊模型
            'fallback_model': './models/fallback_model.glb',
            'xbot_backup': './models/xbot_backup.glb',
            'brain_stem_avatar': './models/brain_stem_avatar.glb',
            'temp_model': './models/temp_model.glb'
        };

        // 测试结果记录
        const testResults = {
            gltfLoader: false,
            threeJS: false,
            modelPaths: false,
            digitalHuman: false
        };

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            
            const logLine = document.createElement('div');
            logLine.style.color = color;
            logLine.textContent = `[${timestamp}] ${message}`;
            element.appendChild(logLine);
            element.scrollTop = element.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateTestSection(sectionId, success) {
            const section = document.getElementById(sectionId);
            section.className = `test-section ${success ? 'success' : 'error'}`;
        }

        // 1. 测试 GLTFLoader
        function testGLTFLoader() {
            const logId = 'gltf-log';
            log(logId, '开始测试 GLTFLoader...');

            try {
                // 检查 THREE 是否存在
                if (typeof THREE === 'undefined') {
                    throw new Error('THREE.js 未加载');
                }
                log(logId, '✅ THREE.js 已加载', 'success');

                // 检查 GLTFLoader
                if (typeof THREE.GLTFLoader === 'undefined') {
                    throw new Error('THREE.GLTFLoader 不可用');
                }
                log(logId, '✅ THREE.GLTFLoader 可用', 'success');

                // 尝试创建实例
                const loader = new THREE.GLTFLoader();
                log(logId, '✅ GLTFLoader 实例创建成功', 'success');

                // 检查关键方法
                if (typeof loader.load !== 'function') {
                    throw new Error('GLTFLoader.load 方法不可用');
                }
                log(logId, '✅ GLTFLoader.load 方法可用', 'success');

                testResults.gltfLoader = true;
                updateTestSection('test-gltf-loader', true);
                log(logId, '🎉 GLTFLoader 测试通过!', 'success');

            } catch (error) {
                testResults.gltfLoader = false;
                updateTestSection('test-gltf-loader', false);
                log(logId, `❌ GLTFLoader 测试失败: ${error.message}`, 'error');
            }
        }

        // 2. 测试 Three.js 核心功能
        function testThreeJS() {
            const logId = 'threejs-log';
            log(logId, '开始测试 Three.js 核心功能...');

            try {
                // 测试基本类
                const scene = new THREE.Scene();
                log(logId, '✅ Scene 创建成功', 'success');

                const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
                log(logId, '✅ Camera 创建成功', 'success');

                const renderer = new THREE.WebGLRenderer();
                log(logId, '✅ Renderer 创建成功', 'success');

                // 测试 AnimationMixer
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial();
                const mesh = new THREE.Mesh(geometry, material);
                
                const mixer = new THREE.AnimationMixer(mesh);
                log(logId, '✅ AnimationMixer 创建成功', 'success');

                // 测试 stopAllAction 方法
                if (typeof mixer.stopAllAction === 'function') {
                    mixer.stopAllAction();
                    log(logId, '✅ mixer.stopAllAction() 方法可用', 'success');
                } else {
                    throw new Error('mixer.stopAllAction() 方法不存在');
                }

                testResults.threeJS = true;
                updateTestSection('test-three-js', true);
                log(logId, '🎉 Three.js 核心功能测试通过!', 'success');

            } catch (error) {
                testResults.threeJS = false;
                updateTestSection('test-three-js', false);
                log(logId, `❌ Three.js 测试失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试模型文件路径
        async function testModelPaths() {
            const logId = 'models-log';
            log(logId, '开始检测模型文件...');

            let availableModels = 0;
            const totalModels = Object.keys(MODEL_PATHS).length;

            for (const [name, path] of Object.entries(MODEL_PATHS)) {
                try {
                    log(logId, `检测模型: ${name} (${path})`);
                    
                    const response = await fetch(path, { method: 'HEAD' });
                    if (response.ok) {
                        log(logId, `✅ ${name} 可访问`, 'success');
                        availableModels++;
                    } else {
                        log(logId, `❌ ${name} 不可访问 (${response.status})`, 'error');
                    }
                } catch (error) {
                    log(logId, `❌ ${name} 检测失败: ${error.message}`, 'error');
                }
            }

            if (availableModels > 0) {
                testResults.modelPaths = true;
                updateTestSection('test-model-paths', true);
                log(logId, `🎉 模型检测完成: ${availableModels}/${totalModels} 可用`, 'success');
            } else {
                testResults.modelPaths = false;
                updateTestSection('test-model-paths', false);
                log(logId, `❌ 没有可用的模型文件`, 'error');
            }
        }

        // 4. 测试数字人系统
        function testDigitalHuman() {
            const logId = 'human-log';
            log(logId, '开始测试数字人系统...');

            try {
                // 检查 EnterpriseDigitalHuman 类
                if (typeof EnterpriseDigitalHuman === 'undefined') {
                    throw new Error('EnterpriseDigitalHuman 类未加载');
                }
                log(logId, '✅ EnterpriseDigitalHuman 类已加载', 'success');

                // 创建实例
                const container = document.getElementById('test-viewport');
                const digitalHuman = new EnterpriseDigitalHuman(container, {
                    width: 400,
                    height: 300,
                    modelPath: MODEL_PATHS.fallback_model,
                    enableAudio: false,
                    enableControls: false,
                    
                    onProgress: (progress) => {
                        log(logId, `📥 加载进度: ${Math.round(progress)}%`);
                    },
                    
                    onLoaded: () => {
                        log(logId, '✅ 数字人初始化成功!', 'success');
                        testResults.digitalHuman = true;
                        updateTestSection('test-digital-human', true);
                        
                        // 测试 reloadModel 方法
                        if (typeof digitalHuman.reloadModel === 'function') {
                            log(logId, '✅ reloadModel 方法可用', 'success');
                        } else {
                            log(logId, '⚠️ reloadModel 方法不可用', 'warning');
                        }
                    },
                    
                    onError: (error) => {
                        log(logId, `❌ 数字人初始化失败: ${error}`, 'error');
                        testResults.digitalHuman = false;
                        updateTestSection('test-digital-human', false);
                    }
                });

                log(logId, '✅ 数字人实例创建成功', 'success');

            } catch (error) {
                testResults.digitalHuman = false;
                updateTestSection('test-digital-human', false);
                log(logId, `❌ 数字人系统测试失败: ${error.message}`, 'error');
            }
        }

        // 生成完整报告
        function generateReport() {
            const logId = 'report-log';
            const timestamp = new Date().toLocaleString();
            
            log(logId, `=== 数字人系统修复验证报告 ===`);
            log(logId, `生成时间: ${timestamp}`);
            log(logId, '');
            
            const passed = Object.values(testResults).filter(Boolean).length;
            const total = Object.keys(testResults).length;
            
            log(logId, `总体状态: ${passed}/${total} 项测试通过`, passed === total ? 'success' : 'warning');
            log(logId, '');
            
            log(logId, '详细结果:');
            log(logId, `• GLTFLoader 测试: ${testResults.gltfLoader ? '✅ 通过' : '❌ 失败'}`, testResults.gltfLoader ? 'success' : 'error');
            log(logId, `• Three.js 核心测试: ${testResults.threeJS ? '✅ 通过' : '❌ 失败'}`, testResults.threeJS ? 'success' : 'error');
            log(logId, `• 模型文件检测: ${testResults.modelPaths ? '✅ 通过' : '❌ 失败'}`, testResults.modelPaths ? 'success' : 'error');
            log(logId, `• 数字人系统测试: ${testResults.digitalHuman ? '✅ 通过' : '❌ 失败'}`, testResults.digitalHuman ? 'success' : 'error');
            log(logId, '');
            
            if (passed === total) {
                log(logId, '🎉 所有修复验证通过！系统可以正常使用。', 'success');
            } else {
                log(logId, '⚠️ 部分测试未通过，请检查相关问题。', 'warning');
            }
        }

        // 页面加载完成后自动运行部分测试
        window.addEventListener('load', () => {
            console.log('🚀 修复验证页面已加载');
            setTimeout(() => {
                testGLTFLoader();
                testThreeJS();
            }, 500);
        });
    </script>
</body>
</html>