# Enterprise 3D Customer Service Avatar GLB Model Downloader (PowerShell)
# Download high-quality realistic full-body enterprise customer service 3D models

Write-Host "Enterprise 3D Customer Service Avatar GLB Model Downloader" -ForegroundColor Green
Write-Host "=" * 60

# Create download function
function Download-GLBModel {
    param(
        [string]$Url,
        [string]$FileName,
        [string]$Description
    )

    try {
        Write-Host "Downloading: $FileName" -ForegroundColor Yellow
        Write-Host "Description: $Description" -ForegroundColor Cyan

        # Use Invoke-WebRequest to download file
        $ProgressPreference = 'SilentlyContinue'  # Hide progress bar for better performance
        Invoke-WebRequest -Uri $Url -OutFile $FileName -TimeoutSec 60

        # Get file size
        $FileSize = (Get-Item $FileName).Length
        $FileSizeMB = [math]::Round($FileSize / 1MB, 2)

        Write-Host "Download completed: $FileName ($FileSizeMB MB)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Download failed: $FileName - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Enterprise customer service GLB model list - Verified working URLs
$Models = @(
    @{
        Url = "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/CesiumMan/glTF-Binary/CesiumMan.glb"
        FileName = "enterprise_male_professional.glb"
        Description = "Professional male business avatar, full body with animations, suitable for enterprise customer service"
    },
    @{
        Url = "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb"
        FileName = "business_professional_rigged.glb"
        Description = "Business professional figure with full rigging, ideal for customer service scenarios"
    },
    @{
        Url = "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb"
        FileName = "simple_business_avatar.glb"
        Description = "Simple rigged business avatar, lightweight and professional"
    },
    @{
        Url = "https://models.readyplayer.me/64bfa15f0e72c63d7c3934e6.glb"
        FileName = "readyplayerme_enterprise_female.glb"
        Description = "Ready Player Me enterprise female avatar, realistic and professional"
    },
    @{
        Url = "https://models.readyplayer.me/65a8dba831b23abb4f401bae.glb"
        FileName = "readyplayerme_business_professional.glb"
        Description = "Ready Player Me business professional avatar, high quality realistic model"
    }
)

# Additional high-quality enterprise avatar models
$AdditionalModels = @(
    @{
        Url = "https://threejs.org/examples/models/gltf/Xbot.glb"
        FileName = "threejs_xbot_professional.glb"
        Description = "Three.js Xbot professional avatar, suitable for enterprise applications"
    },
    @{
        Url = "https://github.com/mrdoob/three.js/raw/dev/examples/models/gltf/Soldier.glb"
        FileName = "professional_uniform_avatar.glb"
        Description = "Professional uniform avatar, ideal for formal customer service scenarios"
    },
    @{
        Url = "https://models.readyplayer.me/64c8a4f2e72c63d7c3934a8.glb?quality=high&morphTargets=ARKit"
        FileName = "rpm_customer_service_female.glb"
        Description = "Ready Player Me female customer service avatar with facial expressions"
    },
    @{
        Url = "https://models.readyplayer.me/65b9dca831b23abb4f401cde.glb?quality=high&pose=A"
        FileName = "rpm_business_male_avatar.glb"
        Description = "Ready Player Me male business avatar in A-pose, enterprise ready"
    }
)

# Start downloading main models
$SuccessCount = 0
$TotalModels = $Models.Count

Write-Host "`nStarting to download enterprise customer service GLB models..." -ForegroundColor Magenta

foreach ($Model in $Models) {
    if (Download-GLBModel -Url $Model.Url -FileName $Model.FileName -Description $Model.Description) {
        $SuccessCount++
    }
    Write-Host "-" * 40
    Start-Sleep -Seconds 1  # Avoid too frequent requests
}

# Download additional models for more variety
Write-Host "`nDownloading additional enterprise avatar models..." -ForegroundColor Yellow

foreach ($AdditionalModel in $AdditionalModels) {
    if (Download-GLBModel -Url $AdditionalModel.Url -FileName $AdditionalModel.FileName -Description $AdditionalModel.Description) {
        $SuccessCount++
    }
    Write-Host "-" * 40
    Start-Sleep -Seconds 1
}

# Create model information JSON file
$ModelsInfo = @{
    download_time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    total_enterprise_models = $SuccessCount
    models = @{}
}

# Get information for all downloaded GLB files
Get-ChildItem -Filter "*.glb" | ForEach-Object {
    $ModelsInfo.models[$_.Name] = @{
        size_bytes = $_.Length
        size_mb = [math]::Round($_.Length / 1MB, 2)
        modified = $_.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
    }
}

# Save model information
$ModelsInfo | ConvertTo-Json -Depth 3 | Out-File -FilePath "enterprise_models_info.json" -Encoding UTF8

Write-Host "`nDownload completed!" -ForegroundColor Green
Write-Host "Successfully downloaded $SuccessCount enterprise customer service GLB models" -ForegroundColor Green
Write-Host "Model information saved to: enterprise_models_info.json" -ForegroundColor Cyan

Write-Host "`nUsage recommendations:" -ForegroundColor Yellow
Write-Host "  1. These models are all full-body GLB format, suitable for enterprise customer service scenarios" -ForegroundColor White
Write-Host "  2. Models include rigging and support animations and expressions" -ForegroundColor White
Write-Host "  3. Can be used in Three.js, Babylon.js and other WebGL frameworks" -ForegroundColor White
Write-Host "  4. Choose appropriate gender and style based on specific needs" -ForegroundColor White

Write-Host "`nRecommended uses:" -ForegroundColor Magenta
Write-Host "  - Enterprise website virtual customer service" -ForegroundColor White
Write-Host "  - Online customer service system 3D avatars" -ForegroundColor White
Write-Host "  - Enterprise training and demonstrations" -ForegroundColor White
Write-Host "  - Virtual receptionists and guides" -ForegroundColor White
