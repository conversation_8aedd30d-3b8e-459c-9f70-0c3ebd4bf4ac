# 修复 business_female.glb 模型文件
# 下载一个更好的女性商务模型来替换损坏的文件

Write-Host "🔧 修复 business_female.glb 模型文件" -ForegroundColor Green
Write-Host "=" * 50

# 备份原文件（如果还没有备份）
if (-not (Test-Path "business_female_backup.glb")) {
    if (Test-Path "business_female.glb") {
        Copy-Item "business_female.glb" "business_female_backup.glb"
        Write-Host "✅ 已备份原文件为 business_female_backup.glb" -ForegroundColor Yellow
    }
}

# 候选的女性商务模型URLs
$FemaleModels = @(
    @{
        Url = "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb"
        FileName = "business_female_v1.glb"
        Description = "标准女性人形模型，带骨骼绑定"
    },
    @{
        Url = "https://models.readyplayer.me/64c8a4f2e72c63d7c3934a8.glb?quality=medium"
        FileName = "business_female_v2.glb"
        Description = "Ready Player Me女性商务模型"
    },
    @{
        Url = "https://threejs.org/examples/models/gltf/Xbot.glb"
        FileName = "business_female_v3.glb"
        Description = "Three.js标准人物模型（可用作女性）"
    }
)

$SuccessCount = 0

Write-Host "`n🚀 开始下载候选模型..." -ForegroundColor Magenta

foreach ($Model in $FemaleModels) {
    try {
        Write-Host "`n正在下载: $($Model.Description)" -ForegroundColor Yellow
        Write-Host "URL: $($Model.Url)" -ForegroundColor Cyan
        
        $ProgressPreference = 'SilentlyContinue'
        Invoke-WebRequest -Uri $Model.Url -OutFile $Model.FileName -TimeoutSec 60
        
        $FileSize = (Get-Item $Model.FileName).Length
        $FileSizeMB = [math]::Round($FileSize / 1MB, 2)
        
        Write-Host "✅ 下载成功: $($Model.FileName) ($FileSizeMB MB)" -ForegroundColor Green
        $SuccessCount++
        
    } catch {
        Write-Host "❌ 下载失败: $($Model.FileName) - $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 1
}

# 选择最好的模型作为 business_female.glb
if ($SuccessCount -gt 0) {
    Write-Host "`n🎯 选择最佳模型..." -ForegroundColor Magenta
    
    # 检查下载的文件，选择最大的作为主要模型
    $BestModel = Get-ChildItem -Filter "business_female_v*.glb" | Sort-Object Length -Descending | Select-Object -First 1
    
    if ($BestModel) {
        Copy-Item $BestModel.Name "business_female.glb" -Force
        $BestSizeMB = [math]::Round($BestModel.Length / 1MB, 2)
        Write-Host "✅ 已选择 $($BestModel.Name) 作为新的 business_female.glb ($BestSizeMB MB)" -ForegroundColor Green
        
        # 清理临时文件
        Remove-Item "business_female_v*.glb" -Force
        Write-Host "🧹 已清理临时文件" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ 所有下载都失败了，保持原文件不变" -ForegroundColor Red
}

# 验证新文件
if (Test-Path "business_female.glb") {
    $NewFile = Get-Item "business_female.glb"
    $NewSizeMB = [math]::Round($NewFile.Length / 1MB, 2)
    
    Write-Host "`n📊 文件信息:" -ForegroundColor Cyan
    Write-Host "  文件名: business_female.glb" -ForegroundColor White
    Write-Host "  大小: $NewSizeMB MB" -ForegroundColor White
    Write-Host "  修改时间: $($NewFile.LastWriteTime)" -ForegroundColor White
    
    if ($NewFile.Length -gt 50000) {  # 大于50KB认为是有效文件
        Write-Host "`n🎉 business_female.glb 修复完成！" -ForegroundColor Green
        Write-Host "💡 建议: 现在可以在测试页面中重新测试这个模型" -ForegroundColor Yellow
    } else {
        Write-Host "`n⚠️ 文件仍然很小，可能需要手动替换" -ForegroundColor Yellow
    }
} else {
    Write-Host "`n❌ business_female.glb 文件不存在" -ForegroundColor Red
}

Write-Host "`n🔗 如果问题仍然存在，建议:" -ForegroundColor Magenta
Write-Host "  1. 使用新下载的模型 (threejs_xbot_professional.glb 等)" -ForegroundColor White
Write-Host "  2. 从 Ready Player Me 网站手动下载女性模型" -ForegroundColor White
Write-Host "  3. 使用其他现有的工作模型作为替代" -ForegroundColor White
