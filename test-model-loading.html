<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字人模型加载测试</title>
    <style>
        body { 
            margin: 0; 
            font-family: Arial, sans-serif; 
            background: #f0f0f0;
        }
        .container { 
            display: flex; 
            height: 100vh; 
        }
        .controls { 
            width: 300px; 
            padding: 20px; 
            background: white;
            overflow-y: auto;
        }
        .viewer { 
            flex: 1; 
            position: relative;
        }
        .btn {
            padding: 10px 15px;
            margin: 5px 0;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            height: 200px;
            overflow-y: scroll;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <h3>🤖 数字人模型测试</h3>
            
            <div class="status" id="status">
                状态: 未初始化
            </div>
            
            <h4>选择模型:</h4>
            <select id="model-select" style="width: 100%; padding: 5px; margin-bottom: 10px;">
                <optgroup label="🏢 企业级客服模型">
                    <option value="business_female" selected>专业企业女客服</option>
                    <option value="business_male">专业企业男客服</option>
                    <option value="enterprise_female_rigged">企业女性客服（高级版）</option>
                    <option value="enterprise_male_simple">企业男性客服（简化版）</option>
                </optgroup>
                
                <optgroup label="👔 专业形象模型">
                    <option value="professional_avatar">专业形象代表</option>
                    <option value="realistic_human_backup">真实人像备用</option>
                    <option value="cesium_man_professional">专业版Cesium男士</option>
                    <option value="CesiumMan">标准Cesium人物</option>
                </optgroup>
                
                <optgroup label="🎭 演示和特效模型">
                    <option value="expression_demo">表情演示模型</option>
                    <option value="fox_expression_demo">狐狸表情演示</option>
                    <option value="morph_animation_demo">变形动画演示</option>
                </optgroup>
                
                <optgroup label="🔧 基础和测试模型">
                    <option value="RiggedSimple">简单绑定模型</option>
                    <option value="RiggedSimple_updated">更新版简单模型</option>
                    <option value="RiggedFigure">绑定人形模型</option>
                </optgroup>
                
                <optgroup label="🛡️ 备用和特殊模型">
                    <option value="fallback_model">系统备用模型</option>
                    <option value="xbot_backup">XBot备用模型</option>
                    <option value="brain_stem_avatar">脑干头像模型</option>
                    <option value="temp_model">临时测试模型</option>
                </optgroup>
            </select>
            
            <button class="btn" id="load-btn">加载模型</button>
            <button class="btn" id="reload-btn">重载模型</button>
            <button class="btn" id="clear-log-btn">清除日志</button>
            
            <h4>控制台日志:</h4>
            <div class="log" id="log"></div>
        </div>
        
        <div class="viewer" id="viewer">
            <!-- 3D 渲染区域 -->
        </div>
    </div>

    <!-- 加载必要的库 -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    <script src="./EnterpriseDigitalHuman.js"></script>
    
    <script>
        // 完整模型路径映射
        const MODEL_PATHS = {
            // 企业级客服模型
            'business_female': './models/business_female.glb',
            'business_male': './models/business_male.glb',
            'enterprise_female_rigged': './models/enterprise_female_rigged.glb',
            'enterprise_male_simple': './models/enterprise_male_simple.glb',
            
            // 专业形象模型
            'professional_avatar': './models/professional_avatar.glb',
            'realistic_human_backup': './models/realistic_human_backup.glb',
            'cesium_man_professional': './models/cesium_man_professional.glb',
            'CesiumMan': './models/CesiumMan.glb',
            
            // 演示和特效模型
            'expression_demo': './models/expression_demo.glb',
            'fox_expression_demo': './models/fox_expression_demo.glb',
            'morph_animation_demo': './models/morph_animation_demo.glb',
            
            // 基础和测试模型
            'RiggedSimple': './models/RiggedSimple.glb',
            'RiggedSimple_updated': './models/RiggedSimple_updated.glb',
            'RiggedFigure': './models/RiggedFigure.glb',
            
            // 备用和特殊模型
            'fallback_model': './models/fallback_model.glb',
            'xbot_backup': './models/xbot_backup.glb',
            'brain_stem_avatar': './models/brain_stem_avatar.glb',
            'temp_model': './models/temp_model.glb'
        };

        let digitalHuman = null;
        let currentModel = 'business_female';

        // 日志系统
        const logContainer = document.getElementById('log');
        const statusContainer = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '2px';
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(message);
        }

        function updateStatus(status, type = 'info') {
            statusContainer.textContent = '状态: ' + status;
            statusContainer.style.background = 
                type === 'error' ? '#f8d7da' : 
                type === 'success' ? '#d4edda' : 
                '#d1ecf1';
        }

        // 初始化数字人
        async function initDigitalHuman() {
            try {
                log('🚀 初始化数字人系统...');
                updateStatus('初始化中...');

                const viewer = document.getElementById('viewer');
                const modelPath = MODEL_PATHS[currentModel];

                digitalHuman = new EnterpriseDigitalHuman(viewer, {
                    width: viewer.clientWidth,
                    height: viewer.clientHeight,
                    modelPath: modelPath,
                    enableAudio: false,
                    enableControls: true,
                    
                    onProgress: (progress) => {
                        log(`📥 加载进度: ${Math.round(progress)}%`);
                        updateStatus(`加载中... ${Math.round(progress)}%`);
                    },
                    
                    onLoaded: () => {
                        log('✅ 数字人模型加载完成!', 'success');
                        updateStatus('模型已加载', 'success');
                    },
                    
                    onError: (error) => {
                        log('❌ 数字人加载失败: ' + error, 'error');
                        updateStatus('加载失败', 'error');
                    }
                });

                log('✅ 数字人系统初始化完成', 'success');
                
            } catch (error) {
                log('❌ 初始化失败: ' + error.message, 'error');
                updateStatus('初始化失败', 'error');
            }
        }

        // 加载选中的模型
        async function loadSelectedModel() {
            const selectedModel = document.getElementById('model-select').value;
            const modelPath = MODEL_PATHS[selectedModel];
            
            if (!modelPath) {
                log('❌ 无效的模型选择', 'error');
                return;
            }

            try {
                currentModel = selectedModel;
                log(`🔄 加载模型: ${selectedModel}`);
                updateStatus('加载中...');

                if (digitalHuman && digitalHuman.reloadModel) {
                    log('🔄 使用reloadModel切换模型...');
                    digitalHuman.reloadModel(modelPath);
                } else {
                    log('🆕 首次初始化数字人...');
                    await initDigitalHuman();
                }

                log(`✅ 模型切换完成: ${selectedModel}`, 'success');
                
            } catch (error) {
                log('❌ 模型加载失败: ' + error.message, 'error');
                updateStatus('加载失败', 'error');
            }
        }

        // 绑定事件
        document.getElementById('load-btn').addEventListener('click', loadSelectedModel);
        
        document.getElementById('reload-btn').addEventListener('click', () => {
            if (digitalHuman) {
                log('🔄 重载当前模型...');
                digitalHuman.reloadModel(MODEL_PATHS[currentModel]);
            } else {
                log('⚠️ 请先初始化数字人', 'error');
            }
        });

        document.getElementById('clear-log-btn').addEventListener('click', () => {
            logContainer.innerHTML = '';
        });

        document.getElementById('model-select').addEventListener('change', (e) => {
            currentModel = e.target.value;
            log(`📋 选择模型: ${currentModel}`);
        });

        // 页面加载完成后自动初始化
        window.addEventListener('load', () => {
            log('🌟 页面加载完成，准备初始化...');
            setTimeout(() => {
                initDigitalHuman();
            }, 500);
        });
    </script>
</body>
</html>