<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js版本兼容性测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #34495e;
            padding: 20px;
            border-radius: 10px;
        }
        
        .version-test {
            background: #3498db;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .version-test:hover {
            background: #2980b9;
        }
        
        .result {
            background: #2c3e50;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { color: #2ecc71; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        
        .viewer {
            width: 100%;
            height: 300px;
            background: #1a1a1a;
            border: 2px solid #555;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Three.js版本兼容性测试</h1>
        <p>测试不同版本的Three.js和GLTFLoader组合</p>
        
        <div class="version-test" onclick="testVersion('r128', 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js', 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js')">
            测试 Three.js r128 + 对应GLTFLoader
        </div>
        
        <div class="version-test" onclick="testVersion('r140', 'https://cdnjs.cloudflare.com/ajax/libs/three.js/140/three.min.js', 'https://cdn.jsdelivr.net/npm/three@0.140.0/examples/js/loaders/GLTFLoader.js')">
            测试 Three.js r140 + 对应GLTFLoader
        </div>
        
        <div class="version-test" onclick="testVersion('r150', 'https://cdnjs.cloudflare.com/ajax/libs/three.js/150/three.min.js', 'https://cdn.jsdelivr.net/npm/three@0.150.0/examples/js/loaders/GLTFLoader.js')">
            测试 Three.js r150 + 对应GLTFLoader
        </div>
        
        <div class="version-test" onclick="testLocalLibs()">
            测试本地库文件
        </div>
        
        <div id="result" class="result"></div>
        <div id="viewer" class="viewer"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultElement = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultElement.innerHTML += `<span class="${type}">[${timestamp}] ${message}</span>\n`;
            resultElement.scrollTop = resultElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('result').innerHTML = '';
        }
        
        function clearScripts() {
            // 移除之前加载的Three.js脚本
            const scripts = document.querySelectorAll('script[src*="three"]');
            scripts.forEach(script => script.remove());
            
            // 清除全局变量
            if (window.THREE) {
                delete window.THREE;
            }
            if (window.GLTFLoader) {
                delete window.GLTFLoader;
            }
        }
        
        async function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function testVersion(version, threeUrl, gltfUrl) {
            clearLog();
            log(`🧪 开始测试 Three.js ${version}`, 'info');
            
            try {
                // 清除之前的脚本
                clearScripts();
                
                // 加载Three.js
                log(`加载 Three.js: ${threeUrl}`, 'info');
                await loadScript(threeUrl);
                
                if (typeof THREE !== 'undefined') {
                    log(`✅ Three.js ${THREE.REVISION} 加载成功`, 'success');
                } else {
                    throw new Error('Three.js未正确加载');
                }
                
                // 加载GLTFLoader
                log(`加载 GLTFLoader: ${gltfUrl}`, 'info');
                await loadScript(gltfUrl);
                
                if (typeof THREE.GLTFLoader !== 'undefined') {
                    log(`✅ GLTFLoader 加载成功`, 'success');
                } else {
                    throw new Error('GLTFLoader未正确加载');
                }
                
                // 测试基本功能
                log('测试基本Three.js功能...', 'info');
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera();
                const renderer = new THREE.WebGLRenderer();
                log('✅ 基本Three.js功能正常', 'success');
                
                // 测试GLTFLoader实例化
                log('测试GLTFLoader实例化...', 'info');
                const loader = new THREE.GLTFLoader();
                log('✅ GLTFLoader实例化成功', 'success');
                
                // 测试加载一个简单的在线模型
                log('测试加载在线Duck模型...', 'info');
                const duckUrl = 'https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Duck/glTF-Binary/Duck.glb';
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load(
                        duckUrl,
                        resolve,
                        undefined,
                        (error) => {
                            log(`GLTFLoader错误: ${error.message || error}`, 'error');
                            log(`错误类型: ${typeof error}`, 'error');
                            log(`错误详情: ${JSON.stringify(error, null, 2)}`, 'error');
                            reject(error);
                        }
                    );
                });
                
                log('✅ 在线模型加载成功', 'success');
                
                // 测试本地模型
                log('测试本地simple_business_avatar.glb...', 'info');
                try {
                    const localGltf = await new Promise((resolve, reject) => {
                        loader.load(
                            './models/simple_business_avatar.glb',
                            resolve,
                            undefined,
                            (error) => {
                                log(`本地模型错误: ${error.message || error}`, 'error');
                                log(`错误类型: ${typeof error}`, 'error');
                                reject(error);
                            }
                        );
                    });
                    log('✅ 本地模型加载成功', 'success');
                } catch (localError) {
                    log(`⚠️ 本地模型加载失败: ${localError.message}`, 'warning');
                }
                
                // 创建简单的3D场景
                const viewer = document.getElementById('viewer');
                viewer.innerHTML = '';
                
                scene.background = new THREE.Color(0x222222);
                camera.position.set(0, 0, 5);
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
                viewer.appendChild(renderer.domElement);
                
                // 添加Duck模型到场景
                scene.add(gltf.scene);
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(1, 1, 1);
                scene.add(directionalLight);
                
                // 渲染
                renderer.render(scene, camera);
                
                log(`🎉 Three.js ${version} 测试完全成功！`, 'success');
                
            } catch (error) {
                log(`❌ Three.js ${version} 测试失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
            }
        }
        
        async function testLocalLibs() {
            clearLog();
            log('🧪 测试本地库文件', 'info');
            
            try {
                clearScripts();
                
                // 测试本地Three.js
                log('加载本地 Three.js...', 'info');
                await loadScript('./libs/three.min.js');
                
                if (typeof THREE !== 'undefined') {
                    log(`✅ 本地Three.js ${THREE.REVISION} 加载成功`, 'success');
                } else {
                    throw new Error('本地Three.js未正确加载');
                }
                
                // 测试本地GLTFLoader
                log('加载本地 GLTFLoader...', 'info');
                await loadScript('./libs/GLTFLoader.js');
                
                if (typeof THREE.GLTFLoader !== 'undefined') {
                    log('✅ 本地GLTFLoader 加载成功', 'success');
                } else {
                    throw new Error('本地GLTFLoader未正确加载');
                }
                
                // 测试基本功能
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera();
                const renderer = new THREE.WebGLRenderer();
                const loader = new THREE.GLTFLoader();
                
                log('✅ 本地库基本功能正常', 'success');
                
                // 测试加载本地模型
                log('测试本地模型加载...', 'info');
                const gltf = await new Promise((resolve, reject) => {
                    loader.load(
                        './models/simple_business_avatar.glb',
                        resolve,
                        undefined,
                        (error) => {
                            log(`本地库加载错误: ${error.message || error}`, 'error');
                            log(`错误类型: ${typeof error}`, 'error');
                            log(`错误构造函数: ${error.constructor?.name}`, 'error');
                            reject(error);
                        }
                    );
                });
                
                log('🎉 本地库测试完全成功！', 'success');
                
                // 创建场景显示
                const viewer = document.getElementById('viewer');
                viewer.innerHTML = '';
                
                scene.background = new THREE.Color(0x222222);
                camera.position.set(0, 1, 3);
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
                viewer.appendChild(renderer.domElement);
                
                scene.add(gltf.scene);
                
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(1, 1, 1);
                scene.add(directionalLight);
                
                renderer.render(scene, camera);
                
            } catch (error) {
                log(`❌ 本地库测试失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
            }
        }
        
        // 页面加载时的提示
        window.addEventListener('load', () => {
            log('🚀 版本兼容性测试器已准备就绪', 'info');
            log('点击上方按钮测试不同版本的Three.js', 'info');
        });
    </script>
</body>
</html>
