#!/usr/bin/env python3
"""
企业3D客服形象GLB模型下载脚本
下载高质量的真人风格、全身企业客服3D模型
"""

import os
import requests
import json
from datetime import datetime
from pathlib import Path

class EnterpriseAvatarDownloader:
    def __init__(self, models_dir="./"):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        
        # 企业客服形象GLB模型资源列表
        self.avatar_models = {
            # Ready Player Me 样例模型
            "enterprise_female_professional.glb": {
                "url": "https://models.readyplayer.me/64bfa15f0e72c63d7c3934e6.glb",
                "description": "专业女性企业客服形象，全身模型，适合商务场景",
                "style": "realistic",
                "gender": "female",
                "body_type": "full_body"
            },
            "enterprise_male_professional.glb": {
                "url": "https://models.readyplayer.me/64bfa15f0e72c63d7c3934e7.glb", 
                "description": "专业男性企业客服形象，全身模型，适合商务场景",
                "style": "realistic",
                "gender": "male",
                "body_type": "full_body"
            },
            
            # 备用高质量模型资源
            "business_avatar_female_v2.glb": {
                "url": "https://cdn.jsdelivr.net/gh/KhronosGroup/glTF-Sample-Models@master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb",
                "description": "标准商务女性形象，带骨骼绑定",
                "style": "semi_realistic", 
                "gender": "female",
                "body_type": "full_body"
            },
            
            # Mixamo 风格模型
            "customer_service_avatar_01.glb": {
                "url": "https://github.com/mrdoob/three.js/raw/dev/examples/models/gltf/Soldier.glb",
                "description": "客服代表形象模型，可用于企业场景",
                "style": "realistic",
                "gender": "unisex", 
                "body_type": "full_body"
            },
            
            # 高质量企业形象模型
            "enterprise_receptionist.glb": {
                "url": "https://threejs.org/examples/models/gltf/Xbot.glb",
                "description": "企业前台接待员形象，专业着装",
                "style": "realistic",
                "gender": "unisex",
                "body_type": "full_body"
            }
        }
    
    def download_model(self, filename, model_info):
        """下载单个GLB模型"""
        try:
            print(f"正在下载: {filename}")
            print(f"描述: {model_info['description']}")
            
            response = requests.get(model_info['url'], stream=True, timeout=30)
            response.raise_for_status()
            
            file_path = self.models_dir / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = file_path.stat().st_size
            print(f"✅ 下载完成: {filename} ({file_size / 1024 / 1024:.2f} MB)")
            
            return {
                "filename": filename,
                "size_bytes": file_size,
                "size_mb": round(file_size / 1024 / 1024, 2),
                "download_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "url": model_info['url'],
                "description": model_info['description'],
                "style": model_info['style'],
                "gender": model_info['gender'],
                "body_type": model_info['body_type']
            }
            
        except Exception as e:
            print(f"❌ 下载失败: {filename} - {str(e)}")
            return None
    
    def download_all_models(self):
        """下载所有企业客服GLB模型"""
        print("🚀 开始下载企业3D客服形象GLB模型...")
        print("=" * 60)
        
        downloaded_models = {}
        
        for filename, model_info in self.avatar_models.items():
            result = self.download_model(filename, model_info)
            if result:
                downloaded_models[filename] = result
            print("-" * 40)
        
        # 更新模型信息文件
        self.update_models_info(downloaded_models)
        
        print(f"\n✨ 下载完成! 共下载 {len(downloaded_models)} 个企业客服GLB模型")
        return downloaded_models
    
    def update_models_info(self, new_models):
        """更新模型信息文件"""
        info_file = self.models_dir / "enterprise_models_info.json"
        
        # 读取现有信息
        existing_info = {}
        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    existing_info = json.load(f)
            except:
                existing_info = {}
        
        # 合并新模型信息
        if 'models' not in existing_info:
            existing_info['models'] = {}
        
        existing_info['models'].update(new_models)
        existing_info['last_update'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        existing_info['total_enterprise_models'] = len(existing_info['models'])
        
        # 保存更新后的信息
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(existing_info, f, indent=2, ensure_ascii=False)
        
        print(f"📝 模型信息已更新: {info_file}")

def main():
    """主函数"""
    print("🏢 企业3D客服形象GLB模型下载器")
    print("=" * 50)
    
    downloader = EnterpriseAvatarDownloader()
    downloaded = downloader.download_all_models()
    
    if downloaded:
        print("\n📋 下载的模型列表:")
        for filename, info in downloaded.items():
            print(f"  • {filename} - {info['description']} ({info['size_mb']} MB)")
    
    print("\n💡 使用建议:")
    print("  1. 这些模型都是全身GLB格式，适合企业客服场景")
    print("  2. 模型包含骨骼绑定，支持动画和表情")
    print("  3. 可以在Three.js、Babylon.js等WebGL框架中使用")
    print("  4. 建议根据具体需求选择合适的性别和风格")

if __name__ == "__main__":
    main()
