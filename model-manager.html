<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字人模型管理器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #1e3a8a;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            color: #6b7280;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .category-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .category-title {
            font-size: 1.5em;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
        }
        
        .model-card {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .model-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
        
        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .model-name {
            font-size: 1.1em;
            font-weight: 700;
            color: #1f2937;
        }
        
        .quality-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .quality-premium { background: linear-gradient(135deg, #9333ea, #c084fc); color: white; }
        .quality-high { background: linear-gradient(135deg, #059669, #34d399); color: white; }
        .quality-standard { background: linear-gradient(135deg, #2563eb, #60a5fa); color: white; }
        .quality-basic { background: linear-gradient(135deg, #6b7280, #9ca3af); color: white; }
        .quality-demo { background: linear-gradient(135deg, #ea580c, #fb923c); color: white; }
        .quality-backup, .quality-fallback { background: linear-gradient(135deg, #d97706, #fbbf24); color: white; }
        .quality-special { background: linear-gradient(135deg, #0891b2, #22d3ee); color: white; }
        .quality-temp { background: linear-gradient(135deg, #dc2626, #f87171); color: white; }
        
        .model-description {
            color: #6b7280;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .model-features {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 15px;
        }
        
        .feature-tag {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .model-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-test {
            background: #10b981;
            color: white;
        }
        
        .btn-test:hover {
            background: #059669;
        }
        
        .model-path {
            font-family: monospace;
            font-size: 11px;
            color: #9ca3af;
            margin-top: 10px;
            padding: 8px;
            background: #f3f4f6;
            border-radius: 4px;
            word-break: break-all;
        }
        
        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        @media (max-width: 768px) {
            .models-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🤖 数字人模型管理器</h1>
            <p>管理和浏览所有可用的3D数字人模型</p>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="total-models">0</div>
                <div class="stat-label">总模型数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="enterprise-models">0</div>
                <div class="stat-label">企业级模型</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="demo-models">0</div>
                <div class="stat-label">演示模型</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="categories-count">0</div>
                <div class="stat-label">模型分类</div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="controls">
            <input type="text" class="search-input" id="search-input" placeholder="🔍 搜索模型名称或描述...">
            <select class="filter-select" id="quality-filter">
                <option value="">所有质量等级</option>
                <option value="premium">顶级</option>
                <option value="high">高质量</option>
                <option value="standard">标准</option>
                <option value="basic">基础</option>
                <option value="demo">演示</option>
                <option value="backup">备用</option>
                <option value="special">特殊</option>
            </select>
            <div class="action-buttons">
                <button class="btn btn-test" id="test-all-btn">批量测试</button>
                <button class="btn btn-secondary" id="export-btn">导出清单</button>
            </div>
        </div>
        
        <!-- 模型分类显示 -->
        <div id="models-container"></div>
    </div>

    <script>
        // 完整模型分类数据
        const MODEL_CATEGORIES = {
            '🏢 企业级客服模型': {
                'business_female': {
                    path: './models/business_female.glb',
                    name: '专业企业女客服（李小雅）',
                    description: '商务正装，适合正式客服场景，具备完整的表情和动画系统',
                    quality: 'high',
                    features: ['PBR材质', '完整骨骼', '表情动画', '专业着装']
                },
                'business_male': {
                    path: './models/business_male.glb', 
                    name: '专业企业男客服（王经理）',
                    description: '商务正装，适合企业对外形象展示和高端客服场景',
                    quality: 'high',
                    features: ['PBR材质', '完整骨骼', '手势动画', '商务形象']
                },
                'enterprise_female_rigged': {
                    path: './models/enterprise_female_rigged.glb',
                    name: '企业女性客服（高级绑定版）',
                    description: '完整骨骼绑定，支持复杂动画和表情变形',
                    quality: 'premium',
                    features: ['高级绑定', '表情变形', '全身动画', '专业定制']
                },
                'enterprise_male_simple': {
                    path: './models/enterprise_male_simple.glb',
                    name: '企业男性客服（简化版）',
                    description: '轻量化模型，适合低配环境和移动设备',
                    quality: 'standard',
                    features: ['基础动画', '优化性能', '移动友好']
                }
            },
            
            '👔 专业形象模型': {
                'professional_avatar': {
                    path: './models/professional_avatar.glb',
                    name: '专业形象代表',
                    description: '高质量专业形象，适合重要场合和企业展示',
                    quality: 'premium',
                    features: ['超高质量', '专业着装', '细节丰富', '企业形象']
                },
                'realistic_human_backup': {
                    path: './models/realistic_human_backup.glb',
                    name: '真实人像备用模型',
                    description: '逼真人物模型，真实感强，适合高端应用',
                    quality: 'high',
                    features: ['真实外观', 'PBR材质', '细节纹理', '照片级质量']
                },
                'cesium_man_professional': {
                    path: './models/cesium_man_professional.glb',
                    name: '专业版Cesium男士',
                    description: '基于Cesium优化的专业模型，兼容性优秀',
                    quality: 'high',
                    features: ['Cesium优化', '标准骨骼', '兼容性好', '稳定可靠']
                },
                'CesiumMan': {
                    path: './models/CesiumMan.glb',
                    name: '标准Cesium人物',
                    description: '经典Cesium演示模型，广泛使用的标准模型',
                    quality: 'standard',
                    features: ['标准模型', '广泛兼容', '稳定可靠', '社区支持']
                }
            },
            
            '🎭 演示和特效模型': {
                'expression_demo': {
                    path: './models/expression_demo.glb',
                    name: '表情演示模型',
                    description: '专门用于表情系统演示和测试的模型',
                    quality: 'demo',
                    features: ['丰富表情', '变形目标', '动画演示', '测试专用']
                },
                'fox_expression_demo': {
                    path: './models/fox_expression_demo.glb',
                    name: '狐狸表情演示',
                    description: '卡通狐狸模型，用于表情系统测试和趣味展示',
                    quality: 'demo',
                    features: ['卡通风格', '表情测试', '趣味演示', '非人类角色']
                },
                'morph_animation_demo': {
                    path: './models/morph_animation_demo.glb',
                    name: '变形动画演示',
                    description: '展示高级变形动画技术的演示模型',
                    quality: 'demo',
                    features: ['变形动画', '技术演示', '高级特效', '研发测试']
                }
            },
            
            '🔧 基础和测试模型': {
                'RiggedSimple': {
                    path: './models/RiggedSimple.glb',
                    name: '简单绑定模型',
                    description: '基础测试模型，快速加载，适合开发测试',
                    quality: 'basic',
                    features: ['快速加载', '基础功能', '测试专用', '开发友好']
                },
                'RiggedSimple_updated': {
                    path: './models/RiggedSimple_updated.glb',
                    name: '更新版简单模型',
                    description: '改进版基础模型，修复了原版的一些问题',
                    quality: 'basic',
                    features: ['优化版本', '改进绑定', '稳定性强', 'Bug修复']
                },
                'RiggedFigure': {
                    path: './models/RiggedFigure.glb',
                    name: '绑定人形模型',
                    description: '标准人形测试模型，用于基础功能验证',
                    quality: 'basic',
                    features: ['标准绑定', '测试用途', '轻量模型', '功能验证']
                }
            },
            
            '🛡️ 备用和特殊模型': {
                'fallback_model': {
                    path: './models/fallback_model.glb',
                    name: '系统备用模型',
                    description: '系统故障时的后备模型，确保系统可用性',
                    quality: 'fallback',
                    features: ['故障保护', '极简设计', '高兼容性', '应急使用']
                },
                'xbot_backup': {
                    path: './models/xbot_backup.glb',
                    name: 'XBot备用模型',
                    description: '经典XBot人物备用版，稳定可靠的选择',
                    quality: 'backup',
                    features: ['经典模型', '稳定可靠', '备用方案', '广泛支持']
                },
                'brain_stem_avatar': {
                    path: './models/brain_stem_avatar.glb',
                    name: '脑干头像模型',
                    description: '特殊用途头像模型，可能用于医学或科学展示',
                    quality: 'special',
                    features: ['特殊设计', '头部特化', '科学用途', '专业领域']
                },
                'temp_model': {
                    path: './models/temp_model.glb',
                    name: '临时测试模型',
                    description: '临时开发测试模型，用于实验新功能',
                    quality: 'temp',
                    features: ['临时使用', '开发测试', '实验功能', '不稳定版本']
                }
            }
        };

        // 全局变量
        let currentFilter = '';
        let currentSearch = '';

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateStats();
            renderModels();
            bindEvents();
        });

        // 更新统计信息
        function updateStats() {
            let totalModels = 0;
            let enterpriseModels = 0;
            let demoModels = 0;
            
            Object.entries(MODEL_CATEGORIES).forEach(([category, models]) => {
                const modelCount = Object.keys(models).length;
                totalModels += modelCount;
                
                if (category.includes('企业')) {
                    enterpriseModels += modelCount;
                }
                if (category.includes('演示')) {
                    demoModels += modelCount;
                }
            });
            
            document.getElementById('total-models').textContent = totalModels;
            document.getElementById('enterprise-models').textContent = enterpriseModels;
            document.getElementById('demo-models').textContent = demoModels;
            document.getElementById('categories-count').textContent = Object.keys(MODEL_CATEGORIES).length;
        }

        // 渲染模型
        function renderModels() {
            const container = document.getElementById('models-container');
            container.innerHTML = '';
            
            Object.entries(MODEL_CATEGORIES).forEach(([categoryName, models]) => {
                const filteredModels = Object.entries(models).filter(([id, model]) => {
                    const matchesSearch = !currentSearch || 
                        model.name.toLowerCase().includes(currentSearch.toLowerCase()) ||
                        model.description.toLowerCase().includes(currentSearch.toLowerCase());
                    
                    const matchesFilter = !currentFilter || model.quality === currentFilter;
                    
                    return matchesSearch && matchesFilter;
                });
                
                if (filteredModels.length === 0) return;
                
                const section = document.createElement('div');
                section.className = 'category-section';
                
                section.innerHTML = `
                    <h2 class="category-title">${categoryName}</h2>
                    <div class="models-grid">
                        ${filteredModels.map(([id, model]) => createModelCard(id, model)).join('')}
                    </div>
                `;
                
                container.appendChild(section);
            });
        }

        // 创建模型卡片
        function createModelCard(id, model) {
            const qualityNames = {
                'premium': '顶级',
                'high': '高质量', 
                'standard': '标准',
                'basic': '基础',
                'demo': '演示',
                'backup': '备用',
                'fallback': '后备',
                'special': '特殊',
                'temp': '临时'
            };
            
            return `
                <div class="model-card" onclick="selectModel('${id}')">
                    <div class="model-header">
                        <div class="model-name">${model.name}</div>
                        <div class="quality-badge quality-${model.quality}">
                            ${qualityNames[model.quality]}
                        </div>
                    </div>
                    
                    <div class="model-description">${model.description}</div>
                    
                    <div class="model-features">
                        ${model.features.map(feature => `<span class="feature-tag">${feature}</span>`).join('')}
                    </div>
                    
                    <div class="model-actions">
                        <button class="btn btn-primary" onclick="event.stopPropagation(); loadModel('${id}')">
                            加载模型
                        </button>
                        <button class="btn btn-test" onclick="event.stopPropagation(); testModel('${id}')">
                            测试
                        </button>
                        <button class="btn btn-secondary" onclick="event.stopPropagation(); previewModel('${id}')">
                            预览
                        </button>
                    </div>
                    
                    <div class="model-path">${model.path}</div>
                </div>
            `;
        }

        // 绑定事件
        function bindEvents() {
            document.getElementById('search-input').addEventListener('input', (e) => {
                currentSearch = e.target.value;
                renderModels();
            });
            
            document.getElementById('quality-filter').addEventListener('change', (e) => {
                currentFilter = e.target.value;
                renderModels();
            });
            
            document.getElementById('test-all-btn').addEventListener('click', testAllModels);
            document.getElementById('export-btn').addEventListener('click', exportModelList);
        }

        // 模型操作函数
        function selectModel(id) {
            console.log('选择模型:', id);
        }

        function loadModel(id) {
            const model = findModel(id);
            if (model) {
                window.open(`./index-complete-customer-service.html?model=${id}`, '_blank');
            }
        }

        function testModel(id) {
            const model = findModel(id);
            if (model) {
                window.open(`./test-model-loading.html?model=${id}`, '_blank');
            }
        }

        function previewModel(id) {
            const model = findModel(id);
            if (model) {
                alert(`模型预览：\n\n名称：${model.name}\n描述：${model.description}\n质量：${model.quality}\n特性：${model.features.join(', ')}\n路径：${model.path}`);
            }
        }

        function findModel(id) {
            for (const category of Object.values(MODEL_CATEGORIES)) {
                if (category[id]) {
                    return category[id];
                }
            }
            return null;
        }

        function testAllModels() {
            const totalModels = Object.values(MODEL_CATEGORIES).reduce((total, category) => {
                return total + Object.keys(category).length;
            }, 0);
            
            if (confirm(`确定要测试所有 ${totalModels} 个模型吗？这可能需要一些时间。`)) {
                window.open('./fix-verification.html', '_blank');
            }
        }

        function exportModelList() {
            const data = {
                exportTime: new Date().toISOString(),
                totalModels: Object.values(MODEL_CATEGORIES).reduce((total, category) => {
                    return total + Object.keys(category).length;
                }, 0),
                categories: MODEL_CATEGORIES
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `digital-human-models-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>