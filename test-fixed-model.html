<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复后的模型</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .test-card.success {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .test-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .test-card.loading {
            border-color: #ffc107;
            background: #fff3cd;
        }
        
        .model-viewer {
            width: 100%;
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            background: #f0f0f0;
        }
        
        .model-info {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            text-align: left;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 测试修复后的GLB模型</h1>
            <p>验证 business_female.glb 和其他模型的加载情况</p>
        </div>
        
        <div class="status info">
            <strong>修复说明:</strong> 已将损坏的 business_female.glb (14.75KB) 替换为 RiggedFigure.glb (48.94KB)
        </div>
        
        <div class="test-grid" id="test-grid">
            <!-- 测试卡片将通过JavaScript生成 -->
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="testAllModels()" id="test-all-btn">🚀 测试所有模型</button>
            <button onclick="clearAllTests()">🧹 清空测试</button>
        </div>
        
        <div id="summary" class="status" style="display: none;"></div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 测试模型列表
        const TEST_MODELS = [
            {
                id: 'business_female',
                filename: 'business_female.glb',
                name: '企业女客服 (修复后)',
                description: '刚刚修复的女性商务模型',
                priority: 1
            },
            {
                id: 'simple_business',
                filename: 'simple_business_avatar.glb', 
                name: '简洁商务形象',
                description: '新下载的轻量级模型',
                priority: 2
            },
            {
                id: 'enterprise_male',
                filename: 'enterprise_male_professional.glb',
                name: '企业男性专业',
                description: '新下载的男性模型',
                priority: 2
            },
            {
                id: 'cesium_man',
                filename: 'CesiumMan.glb',
                name: 'CesiumMan',
                description: '经典测试模型',
                priority: 3
            },
            {
                id: 'rigged_figure',
                filename: 'RiggedFigure.glb',
                name: 'RiggedFigure',
                description: '标准绑定模型',
                priority: 3
            },
            {
                id: 'threejs_xbot',
                filename: 'threejs_xbot_professional.glb',
                name: 'Three.js Xbot',
                description: '新下载的高质量模型',
                priority: 2
            }
        ];
        
        let testResults = {};
        let loadedGLTFLoader = false;
        
        // 初始化页面
        function init() {
            console.log('🚀 初始化测试页面...');
            createTestCards();
            
            // 检查Three.js
            if (typeof THREE === 'undefined') {
                showSummary('❌ Three.js未加载，请检查网络连接', 'error');
                return;
            }
            
            console.log(`✅ Three.js已加载，版本: ${THREE.REVISION}`);
            showSummary('✅ 环境检查通过，可以开始测试', 'success');
        }
        
        // 创建测试卡片
        function createTestCards() {
            const grid = document.getElementById('test-grid');
            grid.innerHTML = '';
            
            TEST_MODELS.forEach(model => {
                const card = document.createElement('div');
                card.className = 'test-card';
                card.id = `card-${model.id}`;
                
                card.innerHTML = `
                    <h4>${model.name}</h4>
                    <p style="font-size: 12px; color: #666;">${model.description}</p>
                    <div class="model-viewer" id="viewer-${model.id}">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
                            等待测试...
                        </div>
                    </div>
                    <button onclick="testSingleModel('${model.id}')" id="btn-${model.id}">
                        测试 ${model.filename}
                    </button>
                    <div class="model-info" id="info-${model.id}"></div>
                `;
                
                grid.appendChild(card);
            });
        }
        
        // 显示总结信息
        function showSummary(message, type = 'info') {
            const summary = document.getElementById('summary');
            summary.textContent = message;
            summary.className = `status ${type}`;
            summary.style.display = 'block';
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 更新卡片状态
        function updateCardStatus(modelId, status, message = '') {
            const card = document.getElementById(`card-${modelId}`);
            const info = document.getElementById(`info-${modelId}`);
            
            card.className = `test-card ${status}`;
            
            if (message) {
                info.textContent = message;
            }
        }
        
        // 加载GLTFLoader
        async function loadGLTFLoader() {
            if (loadedGLTFLoader) return;
            
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js';
                script.onload = () => {
                    loadedGLTFLoader = true;
                    console.log('✅ GLTFLoader加载完成');
                    resolve();
                };
                script.onerror = () => {
                    console.error('❌ GLTFLoader加载失败');
                    reject(new Error('GLTFLoader加载失败'));
                };
                document.head.appendChild(script);
            });
        }
        
        // 测试单个模型
        async function testSingleModel(modelId) {
            const model = TEST_MODELS.find(m => m.id === modelId);
            if (!model) return;
            
            console.log(`🔄 开始测试: ${model.name}`);
            updateCardStatus(modelId, 'loading', '正在加载...');
            
            const startTime = performance.now();
            
            try {
                // 确保GLTFLoader已加载
                await loadGLTFLoader();
                
                // 创建3D场景
                const viewer = document.getElementById(`viewer-${modelId}`);
                const scene = new THREE.Scene();
                scene.background = new THREE.Color(0xf0f0f0);
                
                const camera = new THREE.PerspectiveCamera(75, viewer.clientWidth / viewer.clientHeight, 0.1, 1000);
                camera.position.set(0, 1, 3);
                
                const renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
                
                // 清空viewer并添加canvas
                viewer.innerHTML = '';
                viewer.appendChild(renderer.domElement);
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(1, 1, 1);
                scene.add(directionalLight);
                
                // 加载模型
                const loader = new THREE.GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load(
                        `./models/${model.filename}`,
                        resolve,
                        undefined,
                        reject
                    );
                });
                
                const loadTime = performance.now() - startTime;
                
                // 添加模型到场景
                const modelObject = gltf.scene;
                scene.add(modelObject);
                
                // 调整相机位置
                const box = new THREE.Box3().setFromObject(modelObject);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                const maxDim = Math.max(size.x, size.y, size.z);
                
                camera.position.set(center.x, center.y + maxDim * 0.3, center.z + maxDim * 2);
                camera.lookAt(center);
                
                // 渲染
                renderer.render(scene, camera);
                
                // 统计信息
                let meshCount = 0;
                modelObject.traverse((child) => {
                    if (child.isMesh) meshCount++;
                });
                
                // 记录结果
                testResults[modelId] = {
                    success: true,
                    loadTime: Math.round(loadTime),
                    meshCount: meshCount,
                    animations: gltf.animations ? gltf.animations.length : 0
                };
                
                updateCardStatus(modelId, 'success', 
                    `✅ 加载成功\n加载时间: ${Math.round(loadTime)}ms\n网格数: ${meshCount}\n动画数: ${gltf.animations ? gltf.animations.length : 0}`);
                
                console.log(`✅ ${model.name} 测试成功`);
                
            } catch (error) {
                console.error(`❌ ${model.name} 测试失败:`, error);
                
                testResults[modelId] = {
                    success: false,
                    error: error.message
                };
                
                updateCardStatus(modelId, 'error', 
                    `❌ 加载失败\n错误: ${error.message}`);
            }
        }
        
        // 测试所有模型
        async function testAllModels() {
            const testBtn = document.getElementById('test-all-btn');
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';
            
            showSummary('🚀 开始批量测试所有模型...', 'info');
            
            // 按优先级排序测试
            const sortedModels = [...TEST_MODELS].sort((a, b) => a.priority - b.priority);
            
            for (const model of sortedModels) {
                await testSingleModel(model.id);
                // 短暂延迟避免过快请求
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            // 生成总结
            const successCount = Object.values(testResults).filter(r => r.success).length;
            const totalCount = Object.keys(testResults).length;
            
            if (successCount === totalCount) {
                showSummary(`🎉 所有测试通过！(${successCount}/${totalCount})`, 'success');
            } else {
                showSummary(`⚠️ 部分测试失败 (${successCount}/${totalCount})`, 'error');
            }
            
            testBtn.disabled = false;
            testBtn.textContent = '🚀 测试所有模型';
            
            console.log('📊 测试结果:', testResults);
        }
        
        // 清空所有测试
        function clearAllTests() {
            testResults = {};
            createTestCards();
            document.getElementById('summary').style.display = 'none';
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
